package userdata

import (
	"testing"
)

func TestGetCurrentDealerCard(t *testing.T) {
	user := New()
	user.ID = 12345
	gameID := int32(1001)

	// 测试获取当前庄家牌（应该自动生成）
	card1 := user.GetCurrentDealerCard(gameID)
	if card1 < 1 || card1 > 38 {
		t.<PERSON><PERSON><PERSON>("庄家牌ID应该在1-38范围内，实际值: %.0f", card1)
	}

	// 再次获取应该返回相同的值
	card2 := user.GetCurrentDealerCard(gameID)
	if card1 != card2 {
		t.Errorf("多次获取当前庄家牌应该返回相同值，第一次: %.0f, 第二次: %.0f", card1, card2)
	}
}

func TestGetNextDealerCard(t *testing.T) {
	user := New()
	user.ID = 12345
	gameID := int32(1001)

	// 测试获取下一轮庄家牌（应该自动生成）
	card1 := user.GetNextDealerCard(gameID)
	if card1 < 1 || card1 > 38 {
		t.Errorf("下一轮庄家牌ID应该在1-38范围内，实际值: %.0f", card1)
	}

	// 再次获取应该返回相同的值
	card2 := user.GetNextDealerCard(gameID)
	if card1 != card2 {
		t.Errorf("多次获取下一轮庄家牌应该返回相同值，第一次: %.0f, 第二次: %.0f", card1, card2)
	}
}

func TestAdvanceDealerCardRound(t *testing.T) {
	user := New()
	user.ID = 12345
	gameID := int32(1001)

	// 初始化庄家牌
	user.InitializeDealerCards(gameID)

	originalNext := user.GetNextDealerCard(gameID)

	// 推进轮次
	user.AdvanceDealerCardRound(gameID)

	newCurrent := user.GetCurrentDealerCard(gameID)
	newNext := user.GetNextDealerCard(gameID)

	// 验证当前庄家牌变成了原来的下一轮庄家牌
	if newCurrent != originalNext {
		t.Errorf("推进轮次后，当前庄家牌应该是原来的下一轮庄家牌。期望: %.0f, 实际: %.0f", originalNext, newCurrent)
	}

	// 验证生成了新的下一轮庄家牌
	if newNext == originalNext {
		t.Errorf("推进轮次后，应该生成新的下一轮庄家牌。原值: %.0f, 新值: %.0f", originalNext, newNext)
	}

	// 验证新的下一轮庄家牌在有效范围内
	if newNext < 1 || newNext > 38 {
		t.Errorf("新的下一轮庄家牌ID应该在1-38范围内，实际值: %.0f", newNext)
	}
}

func TestSetDealerCard(t *testing.T) {
	user := New()
	user.ID = 12345
	gameID := int32(1001)

	testCard := 25.0
	user.SetDealerCard(gameID, testCard)

	retrievedCard := user.GetCurrentDealerCard(gameID)
	if retrievedCard != testCard {
		t.Errorf("设置庄家牌失败。期望: %.0f, 实际: %.0f", testCard, retrievedCard)
	}
}

func TestSetNextDealerCard(t *testing.T) {
	user := New()
	user.ID = 12345
	gameID := int32(1001)

	testCard := 30.0
	user.SetNextDealerCard(gameID, testCard)

	retrievedCard := user.GetNextDealerCard(gameID)
	if retrievedCard != testCard {
		t.Errorf("设置下一轮庄家牌失败。期望: %.0f, 实际: %.0f", testCard, retrievedCard)
	}
}

func TestInitializeDealerCards(t *testing.T) {
	user := New()
	user.ID = 12345
	gameID := int32(1001)

	// 初始化庄家牌
	user.InitializeDealerCards(gameID)

	// 验证当前庄家牌已设置
	currentCard := user.GetCurrentDealerCard(gameID)
	if currentCard < 1 || currentCard > 38 {
		t.Errorf("初始化后当前庄家牌ID应该在1-38范围内，实际值: %.0f", currentCard)
	}

	// 验证下一轮庄家牌已设置
	nextCard := user.GetNextDealerCard(gameID)
	if nextCard < 1 || nextCard > 38 {
		t.Errorf("初始化后下一轮庄家牌ID应该在1-38范围内，实际值: %.0f", nextCard)
	}

	// 验证两张牌不相同（虽然理论上可能相同，但概率很低）
	if currentCard == nextCard {
		t.Logf("警告：当前庄家牌和下一轮庄家牌相同 (%.0f)，这在理论上是可能的但概率很低", currentCard)
	}
}

func TestMultipleGames(t *testing.T) {
	user := New()
	user.ID = 12345

	gameIDs := []int32{1001, 1002, 1003}
	cards := make(map[int32]float64)

	// 为每个游戏设置不同的庄家牌
	for i, gameID := range gameIDs {
		testCard := float64(10 + i*5) // 10, 15, 20
		user.SetDealerCard(gameID, testCard)
		cards[gameID] = testCard
	}

	// 验证每个游戏的庄家牌都正确设置
	for _, gameID := range gameIDs {
		retrievedCard := user.GetCurrentDealerCard(gameID)
		expectedCard := cards[gameID]
		if retrievedCard != expectedCard {
			t.Errorf("游戏 %d 的庄家牌设置失败。期望: %.0f, 实际: %.0f", gameID, expectedCard, retrievedCard)
		}
	}
}

func TestGenerateDealerCard(t *testing.T) {
	user := New()
	user.ID = 12345

	// 生成多张庄家牌，验证都在有效范围内
	for i := 0; i < 100; i++ {
		card := user.generateDealerCard()
		if card < 1 || card > 38 {
			t.Errorf("生成的庄家牌ID应该在1-38范围内，实际值: %.0f", card)
		}
	}
}

func TestDealerCardRoundFlow(t *testing.T) {
	user := New()
	user.ID = 12345
	gameID := int32(1001)

	// 模拟完整的游戏轮次流程
	user.InitializeDealerCards(gameID)

	// 记录初始状态
	round1Next := user.GetNextDealerCard(gameID)

	// 推进到第二轮
	user.AdvanceDealerCardRound(gameID)
	round2Current := user.GetCurrentDealerCard(gameID)
	round2Next := user.GetNextDealerCard(gameID)

	// 验证第二轮的当前牌是第一轮的下一轮牌
	if round2Current != round1Next {
		t.Errorf("第二轮当前牌应该是第一轮的下一轮牌。期望: %.0f, 实际: %.0f", round1Next, round2Current)
	}

	// 推进到第三轮
	user.AdvanceDealerCardRound(gameID)
	round3Current := user.GetCurrentDealerCard(gameID)

	// 验证第三轮的当前牌是第二轮的下一轮牌
	if round3Current != round2Next {
		t.Errorf("第三轮当前牌应该是第二轮的下一轮牌。期望: %.0f, 实际: %.0f", round2Next, round3Current)
	}
}

// 基准测试
func BenchmarkGetCurrentDealerCard(b *testing.B) {
	user := New()
	user.ID = 12345
	gameID := int32(1001)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		user.GetCurrentDealerCard(gameID)
	}
}

func BenchmarkAdvanceDealerCardRound(b *testing.B) {
	user := New()
	user.ID = 12345
	gameID := int32(1001)
	user.InitializeDealerCards(gameID)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		user.AdvanceDealerCardRound(gameID)
	}
}
