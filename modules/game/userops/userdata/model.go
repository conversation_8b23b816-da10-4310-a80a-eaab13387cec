package userdata

import (
	"math/rand"
	"time"

	"github.com/jfcwrlight/core/infra/mgdb/orm"
	"github.com/jfcwrlight/core/utils"
	"github.com/jfcwrlight/core/utils/idgen"
)

type M struct {
	ID  int64 `bson:"_id"`
	Tag struct {
		EventID uint64
		SN      uint64
	} `bson:"-"`
	Spin struct {
		LastSpinInfo map[int32]*SpinInfo
		LastWin      map[int32]float64
		Step         map[int32]string
		IbetsChange  map[int32]string
		orm.DirtyTag `bson:"-"`
	}
	KingGame struct {
		Account      string
		Password     string
		Token        string
		orm.DirtyTag `bson:"-"`
	}
	SmallGame struct {
		Status       map[int32]string
		SpinData     map[int32]string
		CurWin       map[int32]float64
		Output       map[int32]float64
		Card         map[int32][]float64
		Av           map[int32]float64
		NextAv       map[int32]float64
		orm.DirtyTag `bson:"-"`
	}
	Settings struct {
		Setting      map[int32]string
		orm.DirtyTag `bson:"-"`
	}
	RegTime      int64
	orm.DirtyTag `bson:"-"`
}

func New() *M {
	return utils.DeepNew[*M]()
}

func (m M) MongoID() any {
	return m.ID
}

func (m *M) ResetEventID() {
	m.Tag.EventID = idgen.NewUUID()
}

func (m *M) EventID() uint64 {
	return m.Tag.EventID
}

type SpinInfo struct {
	Seed         int64
	Index        int32
	IndexMax     int32
	Input        float64
	Private      string
	Policy       string
	BlockHash    string
	BlockNumber  int64
	OracleKey    string
	Mode         int32
	Currency     int32
	orm.DirtyTag `bson:"-"`
}

// GetCurrentDealerCard 获取当前轮次的庄家牌ID
func (m *M) GetCurrentDealerCard(gameID int32) float64 {
	if m.SmallGame.Av == nil {
		m.SmallGame.Av = make(map[int32]float64)
	}

	// 如果当前轮次没有庄家牌，生成一个
	if _, exists := m.SmallGame.Av[gameID]; !exists {
		m.SmallGame.Av[gameID] = m.generateDealerCard()
		m.SmallGame.MarkDirty()
	}

	return m.SmallGame.Av[gameID]
}

// GetNextDealerCard 获取下一轮次的庄家牌ID
func (m *M) GetNextDealerCard(gameID int32) float64 {
	if m.SmallGame.NextAv == nil {
		m.SmallGame.NextAv = make(map[int32]float64)
	}

	// 如果下一轮次没有庄家牌，生成一个
	if _, exists := m.SmallGame.NextAv[gameID]; !exists {
		m.SmallGame.NextAv[gameID] = m.generateDealerCard()
		m.SmallGame.MarkDirty()
	}

	return m.SmallGame.NextAv[gameID]
}

// AdvanceDealerCardRound 推进庄家牌轮次
// 将 nextav 的值赋给 av，并生成新的 nextav 值
func (m *M) AdvanceDealerCardRound(gameID int32) {
	if m.SmallGame.Av == nil {
		m.SmallGame.Av = make(map[int32]float64)
	}
	if m.SmallGame.NextAv == nil {
		m.SmallGame.NextAv = make(map[int32]float64)
	}

	// 将下一轮的庄家牌设为当前轮
	if nextCard, exists := m.SmallGame.NextAv[gameID]; exists {
		m.SmallGame.Av[gameID] = nextCard
	} else {
		// 如果没有下一轮庄家牌，生成一个作为当前轮
		m.SmallGame.Av[gameID] = m.generateDealerCard()
	}

	// 生成新的下一轮庄家牌
	m.SmallGame.NextAv[gameID] = m.generateDealerCard()
	m.SmallGame.MarkDirty()
}

// SetDealerCard 设置当前轮次的庄家牌ID
func (m *M) SetDealerCard(gameID int32, cardID float64) {
	if m.SmallGame.Av == nil {
		m.SmallGame.Av = make(map[int32]float64)
	}

	m.SmallGame.Av[gameID] = cardID
	m.SmallGame.MarkDirty()
}

// SetNextDealerCard 设置下一轮次的庄家牌ID
func (m *M) SetNextDealerCard(gameID int32, cardID float64) {
	if m.SmallGame.NextAv == nil {
		m.SmallGame.NextAv = make(map[int32]float64)
	}

	m.SmallGame.NextAv[gameID] = cardID
	m.SmallGame.MarkDirty()
}

// InitializeDealerCards 初始化庄家牌（当前轮和下一轮）
func (m *M) InitializeDealerCards(gameID int32) {
	if m.SmallGame.Av == nil {
		m.SmallGame.Av = make(map[int32]float64)
	}
	if m.SmallGame.NextAv == nil {
		m.SmallGame.NextAv = make(map[int32]float64)
	}

	// 只有在没有设置的情况下才初始化
	if _, exists := m.SmallGame.Av[gameID]; !exists {
		m.SmallGame.Av[gameID] = m.generateDealerCard()
	}
	if _, exists := m.SmallGame.NextAv[gameID]; !exists {
		m.SmallGame.NextAv[gameID] = m.generateDealerCard()
	}

	m.SmallGame.MarkDirty()
}

// generateDealerCard 生成庄家牌ID（1-38范围内的随机数，与现有代码保持兼容）
func (m *M) generateDealerCard() float64 {
	// 使用当前时间作为随机种子，生成1-38范围内的庄家牌ID
	// 这与现有的庄家牌生成逻辑保持一致
	source := rand.NewSource(time.Now().UnixNano())
	r := rand.New(source)
	return float64(r.Intn(38) + 1)
}
