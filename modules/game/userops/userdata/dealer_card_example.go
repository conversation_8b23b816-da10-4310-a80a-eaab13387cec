package userdata

import (
	"fmt"
	"log"
)

// 示例：如何使用庄家牌轮次管理功能

// ExampleDealerCardUsage 展示庄家牌管理的使用方法
func ExampleDealerCardUsage() {
	// 创建用户数据实例
	user := New()
	user.ID = 12345
	gameID := int32(1001)

	fmt.Println("=== 庄家牌轮次管理示例 ===")

	// 1. 初始化庄家牌
	fmt.Println("\n1. 初始化庄家牌")
	user.InitializeDealerCards(gameID)
	currentCard := user.GetCurrentDealerCard(gameID)
	nextCard := user.GetNextDealerCard(gameID)
	fmt.Printf("初始化后 - 当前庄家牌: %.0f, 下一轮庄家牌: %.0f\n", currentCard, nextCard)

	// 2. 获取当前轮次的庄家牌
	fmt.Println("\n2. 获取当前轮次庄家牌")
	currentCard = user.GetCurrentDealerCard(gameID)
	fmt.Printf("当前轮次庄家牌ID: %.0f\n", currentCard)

	// 3. 获取下一轮次的庄家牌
	fmt.Println("\n3. 获取下一轮次庄家牌")
	nextCard = user.GetNextDealerCard(gameID)
	fmt.Printf("下一轮次庄家牌ID: %.0f\n", nextCard)

	// 4. 推进到下一轮
	fmt.Println("\n4. 推进庄家牌轮次")
	fmt.Printf("推进前 - 当前: %.0f, 下一轮: %.0f\n", 
		user.GetCurrentDealerCard(gameID), 
		user.GetNextDealerCard(gameID))
	
	user.AdvanceDealerCardRound(gameID)
	
	fmt.Printf("推进后 - 当前: %.0f, 下一轮: %.0f\n", 
		user.GetCurrentDealerCard(gameID), 
		user.GetNextDealerCard(gameID))

	// 5. 手动设置庄家牌
	fmt.Println("\n5. 手动设置庄家牌")
	user.SetDealerCard(gameID, 25.0)
	user.SetNextDealerCard(gameID, 30.0)
	fmt.Printf("手动设置后 - 当前: %.0f, 下一轮: %.0f\n", 
		user.GetCurrentDealerCard(gameID), 
		user.GetNextDealerCard(gameID))

	// 6. 再次推进轮次
	fmt.Println("\n6. 再次推进轮次")
	user.AdvanceDealerCardRound(gameID)
	fmt.Printf("再次推进后 - 当前: %.0f, 下一轮: %.0f\n", 
		user.GetCurrentDealerCard(gameID), 
		user.GetNextDealerCard(gameID))
}

// ExampleGameRoundSimulation 模拟游戏轮次
func ExampleGameRoundSimulation() {
	user := New()
	user.ID = 67890
	gameID := int32(2001)

	fmt.Println("\n=== 游戏轮次模拟 ===")

	// 初始化
	user.InitializeDealerCards(gameID)
	
	// 模拟5轮游戏
	for round := 1; round <= 5; round++ {
		fmt.Printf("\n--- 第 %d 轮游戏 ---\n", round)
		
		// 获取当前轮次的庄家牌
		currentCard := user.GetCurrentDealerCard(gameID)
		nextCard := user.GetNextDealerCard(gameID)
		
		fmt.Printf("使用庄家牌: %.0f (下一轮预备: %.0f)\n", currentCard, nextCard)
		
		// 模拟游戏逻辑...
		fmt.Printf("游戏进行中...\n")
		
		// 轮次结束，推进到下一轮
		if round < 5 { // 最后一轮不需要推进
			user.AdvanceDealerCardRound(gameID)
			fmt.Printf("轮次推进完成\n")
		}
	}
}

// ExampleMultiGameSupport 展示多游戏支持
func ExampleMultiGameSupport() {
	user := New()
	user.ID = 11111
	
	gameIDs := []int32{1001, 1002, 1003}

	fmt.Println("\n=== 多游戏支持示例 ===")

	// 为每个游戏初始化庄家牌
	for _, gameID := range gameIDs {
		user.InitializeDealerCards(gameID)
		fmt.Printf("游戏 %d - 当前: %.0f, 下一轮: %.0f\n", 
			gameID,
			user.GetCurrentDealerCard(gameID),
			user.GetNextDealerCard(gameID))
	}

	// 推进第一个游戏的轮次
	fmt.Println("\n推进游戏 1001 的轮次:")
	user.AdvanceDealerCardRound(gameIDs[0])
	
	// 显示所有游戏的状态
	fmt.Println("\n推进后各游戏状态:")
	for _, gameID := range gameIDs {
		fmt.Printf("游戏 %d - 当前: %.0f, 下一轮: %.0f\n", 
			gameID,
			user.GetCurrentDealerCard(gameID),
			user.GetNextDealerCard(gameID))
	}
}

// RunAllExamples 运行所有示例
func RunAllExamples() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("示例运行出错: %v", r)
		}
	}()

	ExampleDealerCardUsage()
	ExampleGameRoundSimulation()
	ExampleMultiGameSupport()
	
	fmt.Println("\n=== 所有示例运行完成 ===")
}
