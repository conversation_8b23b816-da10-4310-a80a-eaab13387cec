# 庄家牌轮次管理系统

## 概述

本系统在 userdata 模块中实现了庄家牌的轮次管理功能，使用 `SmallGame.Av` 和 `SmallGame.NextAv` 字段来管理当前轮次和下一轮次的庄家牌。

## 数据结构

在 `userdata.M` 结构体的 `SmallGame` 字段中：

```go
SmallGame struct {
    // ... 其他字段
    Av           map[int32]float64  // 当前轮次的庄家牌ID (gameID -> cardID)
    NextAv       map[int32]float64  // 下一轮次的庄家牌ID (gameID -> cardID)
    // ...
}
```

## 核心方法

### 1. 获取庄家牌

#### `GetCurrentDealerCard(gameID int32) float64`

-   获取当前轮次的庄家牌 ID
-   如果不存在，会自动生成一个新的庄家牌
-   返回值范围：1-38

#### `GetNextDealerCard(gameID int32) float64`

-   获取下一轮次的庄家牌 ID
-   如果不存在，会自动生成一个新的庄家牌
-   返回值范围：1-38

### 2. 设置庄家牌

#### `SetDealerCard(gameID int32, cardID float64)`

-   手动设置当前轮次的庄家牌 ID
-   用于特殊情况下需要指定特定庄家牌的场景

#### `SetNextDealerCard(gameID int32, cardID float64)`

-   手动设置下一轮次的庄家牌 ID
-   用于预设下一轮的庄家牌

### 3. 轮次管理

#### `AdvanceDealerCardRound(gameID int32)`

-   推进庄家牌轮次
-   将 `NextAv[gameID]` 的值赋给 `Av[gameID]`
-   生成新的 `NextAv[gameID]` 值
-   这是核心的轮次推进方法

#### `InitializeDealerCards(gameID int32)`

-   初始化庄家牌系统
-   如果当前轮次和下一轮次的庄家牌都不存在，则生成它们
-   建议在游戏开始时调用

## 使用示例

### 基本使用流程

```go
// 创建用户数据
user := userdata.New()
user.ID = 12345
gameID := int32(1001)

// 1. 初始化庄家牌
user.InitializeDealerCards(gameID)

// 2. 获取当前轮次的庄家牌
currentCard := user.GetCurrentDealerCard(gameID)
fmt.Printf("当前庄家牌: %.0f\n", currentCard)

// 3. 获取下一轮次的庄家牌（用于预览）
nextCard := user.GetNextDealerCard(gameID)
fmt.Printf("下一轮庄家牌: %.0f\n", nextCard)

// 4. 游戏结束后，推进到下一轮
user.AdvanceDealerCardRound(gameID)

// 5. 现在当前庄家牌变成了之前的下一轮庄家牌
newCurrentCard := user.GetCurrentDealerCard(gameID)
fmt.Printf("新的当前庄家牌: %.0f\n", newCurrentCard) // 应该等于之前的 nextCard
```

### 游戏轮次模拟

```go
user := userdata.New()
gameID := int32(1001)

// 初始化
user.InitializeDealerCards(gameID)

// 模拟5轮游戏
for round := 1; round <= 5; round++ {
    // 获取当前轮次的庄家牌
    currentCard := user.GetCurrentDealerCard(gameID)

    // 进行游戏逻辑...
    fmt.Printf("第%d轮使用庄家牌: %.0f\n", round, currentCard)

    // 轮次结束，推进到下一轮
    if round < 5 { // 最后一轮不需要推进
        user.AdvanceDealerCardRound(gameID)
    }
}
```

### 多游戏支持

```go
user := userdata.New()
gameIDs := []int32{1001, 1002, 1003}

// 为每个游戏初始化庄家牌
for _, gameID := range gameIDs {
    user.InitializeDealerCards(gameID)
}

// 每个游戏可以独立管理自己的庄家牌轮次
user.AdvanceDealerCardRound(1001) // 只推进游戏1001的轮次
```

## 特性

1. **自动生成**: 如果庄家牌不存在，系统会自动生成
2. **多游戏支持**: 每个游戏 ID 都有独立的庄家牌管理
3. **轮次预览**: 可以提前查看下一轮的庄家牌
4. **手动控制**: 支持手动设置特定的庄家牌
5. **数据持久化**: 通过 `MarkDirty()` 确保数据变更被保存

## 庄家牌 ID 范围

-   庄家牌 ID 范围：1-38
-   与现有游戏系统保持兼容
-   生成算法使用当前时间作为随机种子

## 注意事项

1. 调用任何修改方法后，系统会自动调用 `MarkDirty()` 标记数据需要保存
2. 建议在游戏开始时调用 `InitializeDealerCards()` 进行初始化
3. 每轮游戏结束后调用 `AdvanceDealerCardRound()` 推进轮次
4. 系统支持多个游戏同时进行，每个游戏的庄家牌管理是独立的

## 测试

运行测试：

```bash
cd modules/game/userops/userdata
go test -v
```

查看示例：

```go
// 在代码中调用
userdata.RunAllExamples()
```
