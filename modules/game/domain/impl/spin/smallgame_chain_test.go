package spin

import (
	"s2/modules/game/userops/userdata"
	"testing"
)

func TestSmallGameChain_DealerGame(t *testing.T) {
	// 创建庄家游戏状态链
	chain := NewSmallGameChain("dealer", 1001, 12345, 100.0)
	
	// 验证初始状态
	if chain.Current.State != StateInit {
		t.<PERSON><PERSON>rf("初始状态应该是 StateInit，实际是 %s", chain.Current.State.String())
	}
	
	// 执行进入双倍游戏动作
	node, err := chain.ExecuteAction("askDouble")
	if err != nil {
		t.Fatalf("执行 askDouble 动作失败: %v", err)
	}
	
	if node.State != StateDoubleEntry {
		t.Errorf("执行 askDouble 后状态应该是 StateDoubleEntry，实际是 %s", node.State.String())
	}
	
	// 验证状态数据
	if curWin, ok := node.Data["curWin"].(float64); !ok || curWin != 100.0 {
		t.<PERSON>rrorf("curWin 应该是 100.0，实际是 %v", node.Data["curWin"])
	}
	
	// 继续到庄家牌状态
	node, err = chain.ExecuteAction("continue")
	if err != nil {
		t.Fatalf("执行 continue 动作失败: %v", err)
	}
	
	if node.State != StateDoubleDealer {
		t.Errorf("执行 continue 后状态应该是 StateDoubleDealer，实际是 %s", node.State.String())
	}
	
	// 验证庄家牌数据
	if av, ok := node.Data["av"].([]int); !ok || len(av) != 5 {
		t.Errorf("av 数据格式错误: %v", node.Data["av"])
	}
	
	// 测试获胜路径
	node, err = chain.ExecuteAction("win")
	if err != nil {
		t.Fatalf("执行 win 动作失败: %v", err)
	}
	
	if node.State != StateDoubleWin {
		t.Errorf("执行 win 后状态应该是 StateDoubleWin，实际是 %s", node.State.String())
	}
	
	// 验证获胜金额
	if curWin, ok := node.Data["curWin"].(float64); !ok || curWin != 200.0 {
		t.Errorf("获胜后 curWin 应该是 200.0，实际是 %v", node.Data["curWin"])
	}
}

func TestSmallGameChain_LosePath(t *testing.T) {
	chain := NewSmallGameChain("dealer", 1001, 12345, 100.0)
	
	// 快速到达庄家牌状态
	chain.ExecuteAction("askDouble")
	chain.ExecuteAction("continue")
	
	// 测试失败路径
	node, err := chain.ExecuteAction("lose")
	if err != nil {
		t.Fatalf("执行 lose 动作失败: %v", err)
	}
	
	if node.State != StateDoubleLose {
		t.Errorf("执行 lose 后状态应该是 StateDoubleLose，实际是 %s", node.State.String())
	}
	
	// 验证失败后金额
	if curWin, ok := node.Data["curWin"].(float64); !ok || curWin != 0.0 {
		t.Errorf("失败后 curWin 应该是 0.0，实际是 %v", node.Data["curWin"])
	}
	
	// 验证是否为终止状态
	if !chain.IsTerminal() {
		t.Error("失败状态应该是终止状态")
	}
}

func TestSmallGameChain_BackToGame(t *testing.T) {
	chain := NewSmallGameChain("dealer", 1001, 12345, 100.0)
	
	// 进入双倍游戏
	chain.ExecuteAction("askDouble")
	
	// 直接返回主游戏
	node, err := chain.ExecuteAction("askDoubleBackToGame")
	if err != nil {
		t.Fatalf("执行 askDoubleBackToGame 动作失败: %v", err)
	}
	
	if node.State != StateBackToGame {
		t.Errorf("执行 askDoubleBackToGame 后状态应该是 StateBackToGame，实际是 %s", node.State.String())
	}
	
	// 验证是否为终止状态
	if !chain.IsTerminal() {
		t.Error("返回主游戏状态应该是终止状态")
	}
}

func TestSmallGameChain_AvailableActions(t *testing.T) {
	chain := NewSmallGameChain("dealer", 1001, 12345, 100.0)
	
	// 初始状态的可用动作
	actions := chain.GetAvailableActions()
	if len(actions) != 1 || actions[0] != "askDouble" {
		t.Errorf("初始状态应该只有 askDouble 动作，实际是 %v", actions)
	}
	
	// 进入双倍游戏后的可用动作
	chain.ExecuteAction("askDouble")
	actions = chain.GetAvailableActions()
	expectedActions := []string{"continue", "askDoubleBackToGame"}
	if len(actions) != 2 {
		t.Errorf("双倍入口状态应该有2个动作，实际有 %d 个", len(actions))
	}
	
	for _, expected := range expectedActions {
		found := false
		for _, action := range actions {
			if action == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("缺少预期的动作: %s", expected)
		}
	}
}

func TestSmallGameChain_CanExecuteAction(t *testing.T) {
	chain := NewSmallGameChain("dealer", 1001, 12345, 100.0)
	
	// 测试有效动作
	if !chain.CanExecuteAction("askDouble") {
		t.Error("初始状态应该可以执行 askDouble 动作")
	}
	
	// 测试无效动作
	if chain.CanExecuteAction("invalidAction") {
		t.Error("初始状态不应该可以执行 invalidAction 动作")
	}
	
	// 状态转换后测试
	chain.ExecuteAction("askDouble")
	if !chain.CanExecuteAction("continue") {
		t.Error("双倍入口状态应该可以执行 continue 动作")
	}
	
	if chain.CanExecuteAction("askDouble") {
		t.Error("双倍入口状态不应该可以执行 askDouble 动作")
	}
}

func TestSmallGameChain_ToJSON(t *testing.T) {
	chain := NewSmallGameChain("dealer", 1001, 12345, 100.0)
	
	// 测试JSON序列化
	jsonData, err := chain.ToJSON()
	if err != nil {
		t.Fatalf("JSON序列化失败: %v", err)
	}
	
	if len(jsonData) == 0 {
		t.Error("JSON数据不应该为空")
	}
	
	// 可以进一步验证JSON内容的正确性
	t.Logf("JSON数据: %s", string(jsonData))
}

func TestSmallGameManager(t *testing.T) {
	manager := NewSmallGameManager()
	
	gameID := int32(1001)
	userID := int64(12345)
	gameType := "dealer"
	initialWin := 100.0
	
	// 创建游戏状态链
	chain1 := manager.GetOrCreateChain(gameID, userID, gameType, initialWin)
	if chain1 == nil {
		t.Fatal("创建游戏状态链失败")
	}
	
	// 再次获取应该返回相同的链
	chain2 := manager.GetOrCreateChain(gameID, userID, gameType, initialWin)
	if chain1 != chain2 {
		t.Error("多次获取应该返回相同的游戏状态链")
	}
	
	// 执行动作
	node, err := manager.ExecuteAction(gameID, userID, "askDouble")
	if err != nil {
		t.Fatalf("通过管理器执行动作失败: %v", err)
	}
	
	if node.State != StateDoubleEntry {
		t.Errorf("执行动作后状态错误: %s", node.State.String())
	}
	
	// 获取当前状态
	currentNode, err := manager.GetCurrentState(gameID, userID)
	if err != nil {
		t.Fatalf("获取当前状态失败: %v", err)
	}
	
	if currentNode != node {
		t.Error("获取的当前状态与执行动作返回的状态不一致")
	}
	
	// 移除游戏状态链
	manager.RemoveChain(gameID, userID)
	
	// 再次获取应该失败
	_, err = manager.GetCurrentState(gameID, userID)
	if err == nil {
		t.Error("移除后获取状态应该失败")
	}
}

func TestSmallGameChainHandler(t *testing.T) {
	handler := NewSmallGameChainHandler()
	user := userdata.New()
	user.ID = 12345
	
	gameID := int32(1001)
	gameType := "dealer"
	lastWin := 100.0
	
	// 测试初始请求
	response, err := handler.HandleSmallGameRequest(user, gameID, gameType, "askDouble", lastWin)
	if err != nil {
		t.Fatalf("处理初始请求失败: %v", err)
	}
	
	if response["state"] != "DoubleEntry" {
		t.Errorf("初始请求后状态应该是 DoubleEntry，实际是 %v", response["state"])
	}
	
	// 测试继续游戏
	response, err = handler.HandleSmallGameRequest(user, gameID, gameType, "askDouble", lastWin)
	if err != nil {
		t.Fatalf("处理继续请求失败: %v", err)
	}
	
	if response["state"] != "Doubledealer" {
		t.Errorf("继续游戏后状态应该是 Doubledealer，实际是 %v", response["state"])
	}
	
	// 验证用户数据更新
	if user.SmallGame.SpinData == nil || user.SmallGame.SpinData[gameID] == "" {
		t.Error("用户游戏数据应该被更新")
	}
}

// 基准测试
func BenchmarkSmallGameChain_ExecuteAction(b *testing.B) {
	chain := NewSmallGameChain("dealer", 1001, 12345, 100.0)
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// 重置到初始状态
		chain.Current = chain.Head
		
		// 执行一系列动作
		chain.ExecuteAction("askDouble")
		chain.ExecuteAction("continue")
		chain.ExecuteAction("win")
	}
}

func BenchmarkSmallGameManager_Operations(b *testing.B) {
	manager := NewSmallGameManager()
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		gameID := int32(1000 + i%100) // 模拟100个不同的游戏
		userID := int64(10000 + i%1000) // 模拟1000个不同的用户
		
		chain := manager.GetOrCreateChain(gameID, userID, "dealer", 100.0)
		manager.ExecuteAction(gameID, userID, "askDouble")
		manager.RemoveChain(gameID, userID)
		
		_ = chain // 避免未使用变量警告
	}
}
