package spin

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"s2/modules/game/userops/userdata"
	"s2/pb"
	"strconv"
	"strings"
)

// SmallGameAdapter 小游戏适配器，用于将链表实现集成到现有系统
type SmallGameAdapter struct {
	chainHandler *SmallGameChainHandler
	enabled      bool // 是否启用链表实现
}

// NewSmallGameAdapter 创建小游戏适配器
func NewSmallGameAdapter() *SmallGameAdapter {
	return &SmallGameAdapter{
		chainHandler: NewSmallGameChainHandler(),
		enabled:      true, // 默认启用链表实现
	}
}

// SetEnabled 设置是否启用链表实现
func (a *SmallGameAdapter) SetEnabled(enabled bool) {
	a.enabled = enabled
}

// ProcessSmallGameRequest 处理小游戏请求，兼容现有协议
func (a *SmallGameAdapter) ProcessSmallGameRequest(
	user *userdata.M,
	body *pb.SmallGameBtaReq,
	smallGameType string,
	lastWin float64,
	spinData map[string]any,
) (map[string]any, []string, float64, error) {

	if !a.enabled {
		return nil, nil, 0, fmt.Errorf("链表实现未启用")
	}

	// 使用链表处理器处理请求
	response, err := a.chainHandler.HandleSmallGameRequest(
		user, body.GameID, smallGameType, body.Choice, lastWin,
	)
	if err != nil {
		return nil, nil, 0, fmt.Errorf("链表处理失败: %w", err)
	}

	// 转换为兼容格式
	smallGameStatus, win, updatedSpinData, err := a.convertToLegacyFormat(response, spinData, body.Choice)
	if err != nil {
		return nil, nil, 0, fmt.Errorf("格式转换失败: %w", err)
	}

	return updatedSpinData, smallGameStatus, win, nil
}

// convertToLegacyFormat 将链表响应转换为现有格式
func (a *SmallGameAdapter) convertToLegacyFormat(
	chainResponse map[string]any,
	originalSpinData map[string]any,
	choice string,
) ([]string, float64, map[string]any, error) {

	state, ok := chainResponse["state"].(string)
	if !ok {
		return nil, 0, nil, fmt.Errorf("无效的状态")
	}

	data, ok := chainResponse["data"].(map[string]any)
	if !ok {
		return nil, 0, nil, fmt.Errorf("无效的数据")
	}

	terminal, _ := chainResponse["terminal"].(bool)

	// 构建 smallGameStatus
	var smallGameStatus []string
	var win float64

	switch state {
	case "DoubleEntry":
		if choice == "askDoubleBackToGame" {
			smallGameStatus = []string{"back", "0"}
		} else {
			smallGameStatus = []string{"init", "0"}
		}

	case "Doubledealer":
		// 根据下一个动作判断结果
		actions, _ := chainResponse["actions"].([]string)
		if contains(actions, "win") && contains(actions, "lose") {
			// 这是一个等待用户选择的状态
			smallGameStatus = []string{"pending", "0"}
		}

	case "DoubleWin":
		smallGameStatus = []string{"win", "2"}
		if curWin, ok := data["curWin"].(float64); ok {
			win = curWin
		}

	case "DoubleLose":
		smallGameStatus = []string{"lose", "0"}
		win = 0

	case "BackToGame":
		smallGameStatus = []string{"back", "0"}
		if curWin, ok := data["curWin"].(float64); ok {
			win = curWin
		}

	default:
		smallGameStatus = []string{"unknown", "0"}
	}

	// 构建兼容的 spinData
	updatedSpinData := a.buildCompatibleSpinData(originalSpinData, chainResponse, choice, terminal)

	return smallGameStatus, win, updatedSpinData, nil
}

// buildCompatibleSpinData 构建兼容的 spinData
func (a *SmallGameAdapter) buildCompatibleSpinData(
	originalSpinData map[string]any,
	chainResponse map[string]any,
	choice string,
	terminal bool,
) map[string]any {

	// 深拷贝原始数据
	spinData := deepCopyMap(originalSpinData)

	state, _ := chainResponse["state"].(string)
	data, _ := chainResponse["data"].(map[string]any)

	// 确保 gs 存在
	if spinData["gs"] == nil {
		spinData["gs"] = make(map[string]any)
	}
	gs := spinData["gs"].(map[string]any)

	// 设置阶段信息
	switch state {
	case "DoubleEntry":
		if choice == "askDoubleBackToGame" {
			gs["phaseCur"] = "Doubleredblack"
			gs["phaseNext"] = "toDouble"
		} else {
			gs["phaseCur"] = "Doubledealer"
			gs["phaseNext"] = "toDouble"
		}

	case "Doubledealer":
		gs["phaseCur"] = "Doubledealer"
		gs["phaseNext"] = "toDouble"

	case "DoubleWin":
		gs["phaseCur"] = "Doubledealer"
		gs["phaseNext"] = "toDouble"

	case "DoubleLose":
		gs["phaseCur"] = "Doubledealer_finished"
		gs["phaseNext"] = "toPaid"

	case "BackToGame":
		gs["phaseCur"] = "back"
		gs["phaseNext"] = "toPaid"

	default:
		gs["phaseCur"] = "basedeal"
		gs["phaseNext"] = "toPaid"
	}

	// 构建 subGameInfo
	subGameInfo := a.buildSubGameInfo(data, choice, state)

	// 设置 subGameInfo
	if choice == "askDouble" || choice == "askDoubleBackToGame" {
		if gs["subGameInfo"] != nil && len(gs["subGameInfo"].([]any)) > 0 {
			gs["subGameInfo"] = append(gs["subGameInfo"].([]any), subGameInfo)
		} else {
			gs["subGameInfo"] = []any{subGameInfo}
		}
	} else {
		gs["subGameInfo"] = []any{subGameInfo}
	}

	// 设置当前赢取金额
	if curWin, ok := data["curWin"].(float64); ok {
		gs["curWin"] = curWin
	}

	return spinData
}

// buildSubGameInfo 构建 subGameInfo
func (a *SmallGameAdapter) buildSubGameInfo(data map[string]any, choice string, state string) map[string]any {
	subGameInfo := map[string]any{
		"category":      "Double",
		"type":          "dealer",
		"attempt":       0,
		"attemptResult": 0,
		"paidWin":       -1,
		"rule":          "color",
		"winLevel":      0,
		"dblhalf":       0,
		"halfWin":       0,
		"onlyToBD":      nil,
		"sendRestore":   nil,
		"set": []any{
			"dealer",
			5,
			100000,
			[]string{"openCardYes"},
		},
	}

	// 从链表数据中提取信息
	if curWin, ok := data["curWin"].(float64); ok {
		subGameInfo["curWin"] = curWin
		subGameInfo["startWin"] = curWin
		subGameInfo["prevWin"] = curWin
	}

	if av, ok := data["av"].([]int); ok {
		subGameInfo["av"] = av
	}

	if cardDealer, ok := data["carddealer"].(int); ok {
		subGameInfo["add"] = map[string]any{
			"carddealer": cardDealer,
			"openDealer": 1,
		}
	}

	if openDealer, ok := data["openDealer"].(int); ok {
		subGameInfo["openDealerCard"] = openDealer
	}

	// 设置用户选择
	if choice == "askDoubleBackToGame" {
		subGameInfo["userChoice"] = "BackToGame"
	} else if choice != "askDouble" {
		subGameInfo["userChoice"] = choice
	} else {
		subGameInfo["userChoice"] = ""
	}

	return subGameInfo
}

// MapChoiceToAction 将用户选择映射到链表动作
func (a *SmallGameAdapter) MapChoiceToAction(choice string, currentState string) string {
	switch choice {
	case "askDouble":
		if currentState == "Init" {
			return "askDouble"
		}
		return "continue"

	case "askDoubleBackToGame":
		return "askDoubleBackToGame"

	case "1", "2", "3", "4":
		// 这里需要根据游戏逻辑判断胜负
		// 使用与原始代码相同的随机逻辑
		return a.determineGameResult(choice)

	case "collect":
		return "collect"

	case "continue":
		return "continue"

	default:
		return choice
	}
}

// 辅助函数
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func deepCopyMap(original map[string]any) map[string]any {
	copy := make(map[string]any)
	for k, v := range original {
		switch val := v.(type) {
		case map[string]any:
			copy[k] = deepCopyMap(val)
		case []any:
			newSlice := make([]any, len(val))
			for i, item := range val {
				if itemMap, ok := item.(map[string]any); ok {
					newSlice[i] = deepCopyMap(itemMap)
				} else {
					newSlice[i] = item
				}
			}
			copy[k] = newSlice
		default:
			copy[k] = v
		}
	}
	return copy
}

// determineGameResult 根据选择确定游戏结果
func (a *SmallGameAdapter) determineGameResult(choice string) string {
	// 使用与原始代码相同的随机逻辑
	// 50% 概率获胜
	if rand.Float64() < 0.5 {
		return "win"
	}
	return "lose"
}

// 强制导入包的辅助函数
func init() {
	_ = json.Marshal
	_ = strconv.Atoi
	_ = strings.Join
}

// GetLegacyGameStatus 获取传统格式的游戏状态
func (a *SmallGameAdapter) GetLegacyGameStatus(chainResponse map[string]any) string {
	state, _ := chainResponse["state"].(string)
	terminal, _ := chainResponse["terminal"].(bool)

	status := "unknown,0"

	switch state {
	case "DoubleWin":
		status = "win,2"
	case "DoubleLose":
		status = "lose,0"
	case "BackToGame":
		status = "back,0"
	case "DoubleEntry", "Doubledealer":
		if terminal {
			status = "back,0"
		} else {
			status = "pending,0"
		}
	}

	return status
}
