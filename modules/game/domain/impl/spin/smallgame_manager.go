package spin

import (
	"encoding/json"
	"fmt"
	"s2/modules/game/userops/userdata"
	"sync"
)

// SmallGameManager 小游戏管理器
type SmallGameManager struct {
	chains map[string]*SmallGameChain // gameID_userID -> chain
	mutex  sync.RWMutex
}

// NewSmallGameManager 创建小游戏管理器
func NewSmallGameManager() *SmallGameManager {
	return &SmallGameManager{
		chains: make(map[string]*SmallGameChain),
	}
}

// GetOrCreateChain 获取或创建游戏状态链
func (m *SmallGameManager) GetOrCreateChain(gameID int32, userID int64, gameType string, initialWin float64) *SmallGameChain {
	key := fmt.Sprintf("%d_%d", gameID, userID)
	
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	if chain, exists := m.chains[key]; exists {
		return chain
	}
	
	chain := NewSmallGameChain(gameType, gameID, userID, initialWin)
	m.chains[key] = chain
	return chain
}

// RemoveChain 移除游戏状态链
func (m *SmallGameManager) RemoveChain(gameID int32, userID int64) {
	key := fmt.Sprintf("%d_%d", gameID, userID)
	
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	delete(m.chains, key)
}

// ExecuteAction 执行游戏动作
func (m *SmallGameManager) ExecuteAction(gameID int32, userID int64, action string) (*GameStateNode, error) {
	key := fmt.Sprintf("%d_%d", gameID, userID)
	
	m.mutex.RLock()
	chain, exists := m.chains[key]
	m.mutex.RUnlock()
	
	if !exists {
		return nil, fmt.Errorf("游戏状态链不存在")
	}
	
	return chain.ExecuteAction(action)
}

// GetCurrentState 获取当前游戏状态
func (m *SmallGameManager) GetCurrentState(gameID int32, userID int64) (*GameStateNode, error) {
	key := fmt.Sprintf("%d_%d", gameID, userID)
	
	m.mutex.RLock()
	chain, exists := m.chains[key]
	m.mutex.RUnlock()
	
	if !exists {
		return nil, fmt.Errorf("游戏状态链不存在")
	}
	
	return chain.GetCurrentState(), nil
}

// 全局小游戏管理器实例
var globalSmallGameManager = NewSmallGameManager()

// SmallGameChainHandler 基于链表的小游戏处理器
type SmallGameChainHandler struct {
	manager *SmallGameManager
}

// NewSmallGameChainHandler 创建小游戏链式处理器
func NewSmallGameChainHandler() *SmallGameChainHandler {
	return &SmallGameChainHandler{
		manager: globalSmallGameManager,
	}
}

// HandleSmallGameRequest 处理小游戏请求（链表版本）
func (h *SmallGameChainHandler) HandleSmallGameRequest(user *userdata.M, gameID int32, gameType string, choice string, lastWin float64) (map[string]any, error) {
	// 获取或创建游戏状态链
	chain := h.manager.GetOrCreateChain(gameID, user.ID, gameType, lastWin)
	
	// 如果是初始请求，先初始化
	if choice == "askDouble" && chain.Current.State == StateInit {
		_, err := chain.ExecuteAction("askDouble")
		if err != nil {
			return nil, fmt.Errorf("初始化游戏失败: %w", err)
		}
	}
	
	// 执行用户选择的动作
	var resultNode *GameStateNode
	var err error
	
	switch choice {
	case "askDouble":
		resultNode, err = chain.ExecuteAction("continue")
	case "askDoubleBackToGame":
		resultNode, err = chain.ExecuteAction("askDoubleBackToGame")
	case "1", "2", "3", "4": // 庄家牌选择
		// 根据游戏逻辑决定胜负
		if h.shouldWin(choice, chain.Current) {
			resultNode, err = chain.ExecuteAction("win")
		} else {
			resultNode, err = chain.ExecuteAction("lose")
		}
	case "collect":
		resultNode, err = chain.ExecuteAction("collect")
	case "continue":
		resultNode, err = chain.ExecuteAction("continue")
	default:
		return nil, fmt.Errorf("未知的选择: %s", choice)
	}
	
	if err != nil {
		return nil, fmt.Errorf("执行动作失败: %w", err)
	}
	
	// 构建响应数据
	response := h.buildResponse(chain, resultNode, choice)
	
	// 如果游戏结束，清理状态链
	if chain.IsTerminal() {
		h.manager.RemoveChain(gameID, user.ID)
	}
	
	// 更新用户数据
	h.updateUserData(user, gameID, chain, resultNode)
	
	return response, nil
}

// shouldWin 判断是否应该获胜（简化的游戏逻辑）
func (h *SmallGameChainHandler) shouldWin(choice string, currentNode *GameStateNode) bool {
	// 这里应该实现具体的游戏逻辑
	// 比如比较用户选择的牌和庄家牌
	
	// 临时实现：50%概率获胜
	return choice == "1" || choice == "3" // 简化逻辑
}

// buildResponse 构建响应数据
func (h *SmallGameChainHandler) buildResponse(chain *SmallGameChain, node *GameStateNode, choice string) map[string]any {
	response := map[string]any{
		"state":     node.State.String(),
		"gameType":  chain.GameType,
		"choice":    choice,
		"data":      node.Data,
		"actions":   chain.GetAvailableActions(),
		"terminal":  chain.IsTerminal(),
	}
	
	// 根据状态添加特定的响应数据
	switch node.State {
	case StateDoubleDealer:
		response["phaseCur"] = "Doubledealer"
		response["phaseNext"] = "toDouble"
		response["subGameInfo"] = []map[string]any{
			{
				"category":       "Double",
				"type":           "dealer",
				"av":             node.Data["av"],
				"carddealer":     node.Data["carddealer"],
				"openDealer":     node.Data["openDealer"],
				"rule":           node.Data["rule"],
				"curWin":         node.Data["curWin"],
				"prevWin":        node.Data["prevWin"],
				"startWin":       node.Data["startWin"],
				"attempt":        0,
				"attemptResult":  0,
				"paidWin":        -1,
				"userChoice":     choice,
				"winLevel":       0,
			},
		}
	case StateDoubleWin:
		response["phaseCur"] = "Doubledealer"
		response["phaseNext"] = "toDouble"
		response["win"] = node.Data["curWin"]
	case StateDoubleLose:
		response["phaseCur"] = "Doubledealer_finished"
		response["phaseNext"] = "toPaid"
		response["win"] = 0
	case StateBackToGame:
		response["phaseCur"] = "back"
		response["phaseNext"] = "toPaid"
		response["win"] = node.Data["curWin"]
	}
	
	return response
}

// updateUserData 更新用户数据
func (h *SmallGameChainHandler) updateUserData(user *userdata.M, gameID int32, chain *SmallGameChain, node *GameStateNode) {
	// 更新当前赢取金额
	if curWin, ok := node.Data["curWin"].(float64); ok {
		if user.SmallGame.CurWin == nil {
			user.SmallGame.CurWin = make(map[int32]float64)
		}
		user.SmallGame.CurWin[gameID] = curWin
	}
	
	// 如果游戏结束，清理SpinData
	if chain.IsTerminal() {
		if user.SmallGame.SpinData != nil {
			delete(user.SmallGame.SpinData, gameID)
		}
	} else {
		// 保存当前状态到SpinData
		if user.SmallGame.SpinData == nil {
			user.SmallGame.SpinData = make(map[int32]string)
		}
		
		stateData, _ := chain.ToJSON()
		user.SmallGame.SpinData[gameID] = string(stateData)
	}
	
	user.SmallGame.MarkDirty()
}

// LoadChainFromUserData 从用户数据加载游戏状态链
func (h *SmallGameChainHandler) LoadChainFromUserData(user *userdata.M, gameID int32, gameType string) (*SmallGameChain, error) {
	if user.SmallGame.SpinData == nil {
		return nil, fmt.Errorf("用户没有游戏数据")
	}
	
	spinDataStr, exists := user.SmallGame.SpinData[gameID]
	if !exists {
		return nil, fmt.Errorf("游戏数据不存在")
	}
	
	var stateData map[string]any
	if err := json.Unmarshal([]byte(spinDataStr), &stateData); err != nil {
		return nil, fmt.Errorf("解析游戏数据失败: %w", err)
	}
	
	// 重建游戏状态链
	lastWin := 0.0
	if user.Spin.LastWin != nil {
		lastWin = user.Spin.LastWin[gameID]
	}
	
	chain := h.manager.GetOrCreateChain(gameID, user.ID, gameType, lastWin)
	
	// 恢复状态（这里需要根据保存的状态数据来恢复）
	// 具体实现取决于状态数据的格式
	
	return chain, nil
}

// GetChainStatus 获取游戏状态链状态
func (h *SmallGameChainHandler) GetChainStatus(gameID int32, userID int64) (map[string]any, error) {
	node, err := h.manager.GetCurrentState(gameID, userID)
	if err != nil {
		return nil, err
	}
	
	return map[string]any{
		"state":           node.State.String(),
		"data":            node.Data,
		"availableActions": h.manager.chains[fmt.Sprintf("%d_%d", gameID, userID)].GetAvailableActions(),
		"isTerminal":      node.IsTerminal,
	}, nil
}
