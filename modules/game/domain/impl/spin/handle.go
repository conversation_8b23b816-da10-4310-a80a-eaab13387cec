package spin

import (
	"encoding/json"
	"igame"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameKing"
	basicKing "igameKing/common/basic"
	"math"
	"math/rand"
	"s2/common/cache"
	"s2/common/web3"
	"s2/define"
	"s2/gsconf"
	"s2/mbi"
	"s2/modules/game/userops/userdata"
	"s2/pb"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/conf/table"
	"github.com/jfcwrlight/core/infra/chdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
)

type BaseSpinData struct {
	GameID   int32
	Mode     int32
	DeskID   int32
	Currency int32
	Input    float64
}

func (uc *useCase) onUserInfoReq(user *userdata.M, body *pb.UserInfoReq, response func(*pb.UserInfoResp, error)) {
	bc, err := cache.QueryUserBasicInfo(user.ID)
	if err != nil {
		log.Error(err)
		response(&pb.UserInfoResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	lobbyinfo := table.Get[gsconf.LobbyInfoConf](1)
	resp, err := message.Request[pb.AssetBalanceResp](bc.ServerID, &pb.AssetBalanceReq{
		UserID: user.ID,
	})
	response(&pb.UserInfoResp{
		Code:         pb.SUCCESS,
		UserID:       user.ID,
		CurrencyList: lobbyinfo.CurrencyList,
		Balance:      resp.Balance,
	}, nil)
}

func (uc *useCase) onKingGameEnterGameReq(user *userdata.M, body *pb.KingGameEnterGameReq, response func(*pb.KingGameEnterGameResp, error)) {
	bc, e := cache.QueryUserBasicInfo(user.ID)
	if e != nil {
		log.Errorf("KingGameEnterGame error uid %vv, err %v", user.ID, e)
		response(&pb.KingGameEnterGameResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	inputInfo := table.Find[gsconf.InputInfoConf](func(iic *gsconf.InputInfoConf) bool {
		return iic.CurrencyID == body.Currency && iic.GameID == body.GameID
	})
	if inputInfo == nil {
		response(&pb.KingGameEnterGameResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	user.KingGame.Account = ""
	if user.KingGame.Account == "" {
		ctx := map[string]any{}
		ctx["account"] = strconv.FormatInt(user.ID, 10)
		ctx["password"] = "**********"
		apiurl := basicKing.ApiBaseURL + "/api/Game/RegisterUser"
		log.Infof("RegisterUser req: %v, %s", ctx, apiurl)
		respBytes, err := basicKing.SendKingHtttpGet(ctx, apiurl)
		if err != nil {
			log.Infof("RegisterUser error: %v", err)
			response(&pb.KingGameEnterGameResp{Code: pb.SERVER_ERROR}, nil)
			return
		}
		resp := map[string]interface{}{}
		err = json.Unmarshal(respBytes, &resp)
		if err != nil {
			log.Infof("king game EnterGame error1: %v, %v", err, respBytes)
			response(&pb.KingGameEnterGameResp{Code: pb.SERVER_ERROR}, nil)
			return
		}
		log.Infof("king game EnterGame resp: %v", resp)
		_, ok := resp["code"].(float64)
		if !ok {
			log.Infof("king game EnterGame error2: %v, %v", resp, respBytes)
			response(&pb.KingGameEnterGameResp{Code: pb.SERVER_ERROR}, nil)
			return
		}
		user.KingGame.Account = resp["account"].(string)
		user.KingGame.Password = resp["password"].(string)
		user.KingGame.MarkDirty()
	}
	resp, err := message.Request[pb.AssetBalanceResp](bc.ServerID, &pb.AssetBalanceReq{
		UserID: user.ID,
	})
	if err != nil {
		log.Errorf("KingGameEnterGame error uid %vv, err %v", user.ID, err)
		response(&pb.KingGameEnterGameResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	balance := resp.Balance[body.Currency]
	ctx := map[string]any{}
	err = json.Unmarshal([]byte(body.Ctx), &ctx)
	if err != nil {
		log.Errorf("king game EnterGame unmarshal error: %v", err)
		response(&pb.KingGameEnterGameResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	ctx["account"] = user.KingGame.Account
	ctx["password"] = user.KingGame.Password
	kingGameID := igameKing.KingGameId(body.GameID)
	ctx["gameid"] = kingGameID
	ctx["levelid"] = kingGameID * 10
	var glodMultiple float64 = 100
	betScores := []float64{}
	line := float64(igameKing.Line(body.GameID))
	for _, v := range inputInfo.ValueList {
		betScores = append(betScores, v/line*glodMultiple)
	}
	// 补充
	ctx["betScores"] = betScores
	ctx["Currency"] = "USD"
	ctx["GlodMultiple"] = glodMultiple
	ctx["balance"] = balance * float64(glodMultiple)
	ctx["GiveUpGame"] = true
	detail := igameKing.EnterGame(body.GameID, ctx)
	if detail == "" {
		log.Errorf("king game EnterGame error: %v", detail)
		response(&pb.KingGameEnterGameResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	detailMap := map[string]any{}
	json.Unmarshal([]byte(detail), &detailMap)
	kingToken := detailMap["data"].(map[string]any)["token"]
	user.KingGame.Token = kingToken.(string)
	// user.KingGame.MarkDirty()
	response(&pb.KingGameEnterGameResp{Code: pb.SUCCESS, Detail: detail}, nil)
}

func (uc *useCase) onGameKingSpinReq(user *userdata.M, body *pb.GameKingSpinReq, response func(*pb.GameSpinResp, error)) {
	inputInfo := table.Find[gsconf.InputInfoConf](func(iic *gsconf.InputInfoConf) bool {
		return iic.CurrencyID == body.Currency && iic.GameID == body.GameID
	})
	if inputInfo == nil {
		response(&pb.GameSpinResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	line := igameKing.Line(body.GameID)
	ctx := map[string]any{}
	err := json.Unmarshal([]byte(body.Ctx), &ctx)
	if err != nil {
		log.Infof("GameKingSpinReq unmarshal error: %v", err)
		response(&pb.GameSpinResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	var glodMultiple float64 = 100
	var input float64 = float64(ctx["gamble"].(float64)) * float64(ctx["lv"].(float64)) * float64(line) / glodMultiple
	var mode int32 = 0
	bBuyFree, ok := ctx["bBuyFree"].(bool)
	if ok && bBuyFree {
		mode = 1
	}
	kingGameID := igameKing.KingGameId(body.GameID)
	ctx["gameid"] = kingGameID
	ctx["levelid"] = kingGameID * 10
	ctx["token"] = user.KingGame.Token
	ctx["account"] = user.KingGame.Account
	betScores := []float64{}
	for _, v := range inputInfo.ValueList {
		betScores = append(betScores, v/float64(line)*glodMultiple)
	}
	ctx["betScores"] = betScores
	resp := uc.gameSpin(user, BaseSpinData{
		GameID:   body.GameID,
		Mode:     mode,
		DeskID:   body.DeskID,
		Currency: body.Currency,
		Input:    input,
	}, ctx)
	var res = []map[string]any{}
	err = json.Unmarshal([]byte(resp.Detail), &res)
	if err == nil {
		res[0]["JackpotInfo"] = resp.JackpotInfo
		res[0]["HashInfo"] = resp.HashInfo
		if len(res) > 1 {
			detail, _ := json.Marshal(res)
			resp.Detail = string(detail)
		} else {
			detail, _ := json.Marshal(res[0])
			resp.Detail = string(detail)
		}
	}
	response(resp, nil)
}

func (uc *useCase) onGameSpinReq(user *userdata.M, body *pb.GameSpinReq, response func(*pb.GameSpinResp, error)) {
	inputInfo := table.Find[gsconf.InputInfoConf](func(iic *gsconf.InputInfoConf) bool {
		return iic.CurrencyID == body.Currency && iic.GameID == body.GameID
	})
	if inputInfo == nil {
		response(&pb.GameSpinResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	input := inputInfo.ValueList[body.SpinIndex]
	resp := uc.gameSpin(user, BaseSpinData{
		GameID:   body.GameID,
		Mode:     body.Mode,
		DeskID:   body.DeskID,
		Currency: body.Currency,
		Input:    input,
	}, nil)
	response(resp, nil)
}

func (uc *useCase) onGamePGSpinReq(user *userdata.M, body *pb.GamePGSpinReq, response func(*pb.GameSpinResp, error)) {
	ctx := basic.SpinContext{}
	ctx["gamble"] = body.Gamble
	ctx["lv"] = body.Lv
	resp := uc.gameSpin(user, BaseSpinData{
		GameID:   body.GameID,
		Mode:     body.Mode,
		DeskID:   body.DeskID,
		Currency: body.Currency,
		Input:    body.Input,
	}, ctx)
	var detail []byte
	var res1 = map[string]any{}
	err := json.Unmarshal([]byte(resp.Detail), &res1)
	if err != nil {
		var res2 = []map[string]any{}
		err = json.Unmarshal([]byte(resp.Detail), &res2)
		if err == nil {
			res2[0]["JackpotInfo"] = resp.JackpotInfo
			res2[0]["HashInfo"] = resp.HashInfo
			detail, _ = json.Marshal(res2)
		}
	} else {
		res1["JackpotInfo"] = resp.JackpotInfo
		res1["HashInfo"] = resp.HashInfo
		detail, _ = json.Marshal(res1)
	}
	resp.Detail = string(detail)
	response(resp, nil)
}

func (uc *useCase) gameSpin(user *userdata.M, baseData BaseSpinData, ctx map[string]any) *pb.GameSpinResp {
	start := time.Now()
	defer func() {
		log.Infof("gameSpin user %d game %d took %s", user.ID, baseData.GameID, time.Since(start))
	}()
	gameinfo := table.Get[gsconf.GameInfoConf](baseData.GameID)
	if gameinfo == nil {
		log.Errorf("gameSpin gameinfo nil uid %v, gameId %v", user.ID, baseData.GameID)
		return &pb.GameSpinResp{
			Code: pb.PARAM_ERROR,
		}
	}
	if !gameinfo.Open {
		log.Errorf("gameSpin gameinfo not open uid %v, gameId %v", user.ID, baseData.GameID)
		return &pb.GameSpinResp{
			Code: pb.SERVER_MAINTENANCE,
		}
	}
	inputInfo := table.Find[gsconf.InputInfoConf](func(iic *gsconf.InputInfoConf) bool {
		return iic.CurrencyID == baseData.Currency && iic.GameID == baseData.GameID
	})
	if inputInfo == nil {
		log.Errorf("gameSpin inputInfo nil uid %v, gameId %v", user.ID, baseData.GameID)
		return &pb.GameSpinResp{
			Code: pb.PARAM_ERROR,
		}
	}
	if baseData.Input <= 0 { //不能是负数，不然会加钱
		log.Errorf("gameSpin input <= 0 gameId %v, input %v", baseData.GameID, baseData.Input)
		return &pb.GameSpinResp{
			Code: pb.PARAM_ERROR,
		}
	}
	lastSpin := user.Spin.LastSpinInfo[baseData.GameID]
	policy := ""
	private := ""
	blockHash := ""
	var blockNumber int64
	oracleKey := ""
	var seed int64
	clientMode := func() int32 {
		if gameinfo.Platform == "xking" {
			return igameKing.ClientMode(baseData.GameID)
		}
		return igame.ClientMode(baseData.GameID)
	}()
	var input float64
	var sub float64 = 0
	var index int32 = 0
	var mode int32 = baseData.Mode
	var currency int32 = baseData.Currency
	var err error
	if lastSpin != nil && lastSpin.IndexMax > lastSpin.Index {
		if clientMode == basic.EnumClientMode.MULTI {
			lastSpin = &userdata.SpinInfo{}
			user.Spin.LastSpinInfo[baseData.GameID] = lastSpin
			seed = lastSpin.Seed
			input = lastSpin.Input
			policy = lastSpin.Policy
			private = lastSpin.Private
			blockHash = lastSpin.BlockHash
			blockNumber = lastSpin.BlockNumber
			oracleKey = lastSpin.OracleKey
			index = lastSpin.Index + 1
			mode = lastSpin.Mode
			currency = lastSpin.Currency
			sub = 0
		} else {
			log.Errorf("lastSpin.SNMax > lastSpin.SN spin client mode %v not support uid %v, gameId %v", clientMode, user.ID, baseData.GameID)
			return &pb.GameSpinResp{
				Code: pb.SERVER_ERROR,
			}
		}
	} else {
		data, ok := uc.hash.Load(pb.CHAIN_OPBNB)
		if !ok {
			log.Errorf("gameSpin hash not found uid %v, gameId %v", user.ID, baseData.GameID)
			return &pb.GameSpinResp{
				Code: pb.SERVER_ERROR,
			}
		}
		bh := data.(*pb.BlockHash)
		blockHash = bh.Hash
		blockNumber = bh.Number
		policy, private, err = web3.NewPolicy(bh.Chain.String(), bh.Number)
		if conf.Str("debug.hash", "") != "" {
			blockHash = conf.Str("debug.hash")
		}
		if conf.Str("debug.private", "") != "" {
			private = conf.Str("debug.private")
		}
		if err != nil {
			log.Errorf("gameSpin newPolicy error uid %v, gameId %v, err %v", user.ID, baseData.GameID, err)
			return &pb.GameSpinResp{
				Code: pb.SERVER_ERROR,
			}
		}
		oracleKey, err = web3.NewOracleKey(private, blockHash)
		if err != nil {
			log.Errorf("gameSpin newOracleKey error uid %v, gameId %v, err %v", user.ID, baseData.GameID, err)
			return &pb.GameSpinResp{
				Code: pb.SERVER_ERROR,
			}
		}
		seed, err = web3.GetOracleSeedFromKey(oracleKey)
		if err != nil {
			log.Errorf("gameSpin getOracleSeedFromKey error uid %v, gameId %v, err %v", user.ID, baseData.GameID, err)
			return &pb.GameSpinResp{
				Code: pb.SERVER_ERROR,
			}
		}
		mode = baseData.Mode
		input = baseData.Input
		index = 1
		coef := func() int32 {
			if gameinfo.Platform == "xking" {
				return igameKing.InputCoef(baseData.GameID, mode)
			}
			return igame.InputCoef(baseData.GameID, mode)
		}()
		sub = input * float64(coef) / 100
	}
	// 记录上一轮的 spin 种子
	if gameinfo.Platform == "xbta" {
		lastSpin = &userdata.SpinInfo{}
		user.Spin.LastSpinInfo[baseData.GameID] = lastSpin
		lastSpin.Seed = seed
		lastSpin.MarkDirty()
	}
	lines := func() int32 {
		if gameinfo.Platform == "xking" {
			return igameKing.Line(baseData.GameID)
		}
		return igame.Line(baseData.GameID)
	}()
	bc, e := cache.QueryUserBasicInfo(user.ID)
	if e != nil {
		log.Errorf("gameSpin queryUserBasicInfo error uid %v, gameId %v, err %v", user.ID, baseData.GameID, err)
		return &pb.GameSpinResp{
			Code: pb.SERVER_ERROR,
		}
	}
	resp, err := message.Request[pb.AssetBalanceResp](bc.ServerID, &pb.AssetBalanceReq{
		UserID: user.ID,
	})
	balance := resp.Balance[currency]
	// balance := uc.AssetCase().Balance(user, body.Currency)
	if balance < sub {
		if gameinfo.Platform != "xking" {
			if gameinfo.Platform == "x" {
				return &pb.GameSpinResp{
					Code: pb.BALANCE_NOT_ENOUGH,
				}
			} else {
				return &pb.GameSpinResp{
					Code:   pb.SUCCESS,
					Detail: igame.Exception(baseData.GameID, 1),
				}
			}
		}
	}
	if ctx == nil {
		ctx = map[string]any{}
	}

	ctx["balance"] = balance
	if gameinfo.Platform != "xking" {
		ctx["lineCount"] = lines
		ctx["input"] = input
		ctx["sub"] = sub
		ctx["uuid"] = strconv.FormatUint(user.EventID(), 10)
		ctx["currency"] = strconv.Itoa(int(currency))
	} else {
		ctx["balance"] = balance * 100 //king 精度100倍
	}
	if sub < 0 {
		log.Errorf("spin sub asset num %v must > 0 uid %v, gameId %v", input, user.ID, baseData.GameID)
		return &pb.GameSpinResp{
			Code: pb.SERVER_ERROR,
		}
	}
	detail := ""
	var output float64 = 0
	var payout int32 = 0
	var payoutTotal int32 = 0
	var indexMax int32 = 0
	log.Infof("spin gameId %v, uid %d, mode %v, input %v, sub %v, balance %v, currency %v, blockNumber %v, blockHash %v, private %v, seed %d, oracleKey %v", baseData.GameID, user.ID, mode, input, sub, balance, currency, blockNumber, blockHash, private, seed, oracleKey)
	var spin any = func() any {
		if gameinfo.Platform == "xking" {
			s, _ := igameKing.SpinData(baseData.GameID, mode, seed, gameinfo.Tax, ctx)
			return s
		}
		s, _ := igame.Spin(baseData.GameID, mode, seed, gameinfo.Tax)
		return s
	}()
	gm, debugJackpot := uc.gmSpin(baseData, ctx, gameinfo)
	if gm != nil {
		spin = gm
	}
	if clientMode == basic.EnumClientMode.ONE {
		spin1, ok := spin.(basic.ISpin)
		if ok {
			detail = spin1.Data(ctx)
			payout = spin1.Payout()
			payoutTotal = payout
			indexMax = 1
		} else {
			spin2, ok := spin.(basicKing.ISpin)
			if ok {
				detail = spin2.Data()
				payout = spin2.Payout()
				payoutTotal = payout
				indexMax = 1
			} else {
				log.Errorf("spin not support uid %v, gameId %v", user.ID, baseData.GameID)
				return &pb.GameSpinResp{
					Code: pb.SERVER_ERROR,
				}
			}
		}
		output = input * float64(payout) / float64(lines)
	} else if clientMode == basic.EnumClientMode.MULTI {
		spin1, ok := spin.(basic.ISpin)
		if ok {
			roundPayout := spin1.RoundPayouts()
			payout = roundPayout[index-1]
			detail = spin1.RoundData(index-1, ctx)
			payoutTotal = payout
			indexMax = int32(len(roundPayout))
		} else {
			spin2, ok := spin.(basicKing.ISpin)
			if ok {
				roundPayout := spin2.RoundPayouts()
				payout = roundPayout[index-1]
				detail = spin2.RoundData(index - 1)
				payoutTotal = payout
				indexMax = int32(len(roundPayout))
			}
		}
		output = input * float64(payout) / float64(lines)
	} else {
		log.Errorf("spin client mode %v not support", clientMode)
		return &pb.GameSpinResp{
			Code: pb.SERVER_ERROR,
		}
	}
	if payout < 0 { //异常
		return &pb.GameSpinResp{
			Code:   pb.SUCCESS,
			Detail: detail,
		}
	}
	log.Infof("spin gameId %v, uid %d, payout %v, output %v", baseData.GameID, user.ID, payout, output)
	var balances map[int32]float64
	var changeAssets []*pb.IDValFloat
	if sub > 0 {
		changeAssets = []*pb.IDValFloat{
			&pb.IDValFloat{ID: int64(currency), Value: -sub},
			&pb.IDValFloat{ID: int64(currency), Value: output},
		}
	} else {
		changeAssets = []*pb.IDValFloat{
			&pb.IDValFloat{ID: int64(currency), Value: -input},
			&pb.IDValFloat{ID: int64(currency), Value: output},
		}
	}
	respChange, err := message.Request[pb.ChangeAssetResp](bc.ServerID, &pb.ChangeAssetReq{
		UserID: user.ID,
		Cause:  "spin",
		Assets: changeAssets,
	})
	if err != nil {
		log.Error(err)
		return &pb.GameSpinResp{
			Code: pb.SERVER_ERROR,
		}
	}
	balances = respChange.Balance
	if lastSpin == nil {
		lastSpin = &userdata.SpinInfo{}
		user.Spin.LastSpinInfo[baseData.GameID] = lastSpin
	}
	if indexMax > index {
		lastSpin.Index = index
		lastSpin.IndexMax = indexMax
		lastSpin.Seed = seed
		lastSpin.Policy = policy
		lastSpin.Input = input
		lastSpin.Private = private
		lastSpin.BlockHash = blockHash
		lastSpin.BlockNumber = blockNumber
		lastSpin.OracleKey = oracleKey
		lastSpin.Mode = baseData.Mode
		lastSpin.Currency = currency
		lastSpin.MarkDirty()
	}
	user.Tag.SN++
	extInfo, _ := json.Marshal(ctx)
	var jackpotInfo *pb.JackpotSpin
	if gameinfo.JackPotOpen > 0 && index == 1 && sub > 0 {
		resp, err := message.RequestAny[pb.DefiTryJackpotResp](define.ModuleName.Defi, &pb.DefiTryJackpotReq{
			Uid:    user.ID,
			GameID: baseData.GameID,
			Input:  input,
			SeatID: baseData.DeskID,
			X:      output / sub,
			Debug:  debugJackpot,
		})
		if err != nil {
			log.Error("DefiTryJackpotResp Error:", err)
		} else {
			jackpotInfo = resp.Jackpot
		}
	}
	if indexMax <= index { //最后一轮记录日志
		coef := func() int32 {
			if gameinfo.Platform == "xking" {
				return igameKing.InputCoef(baseData.GameID, mode)
			}
			return igame.InputCoef(baseData.GameID, mode)
		}()
		in := input * float64(coef) / 100
		out := input * float64(payoutTotal) / float64(lines)
		playData := &pb.StatisticsPlayData{
			EventID:     user.EventID(),
			UserID:      user.ID,
			GameId:      gameinfo.ID,
			Platfrom:    gameinfo.Platform,
			Mode:        mode,
			Input:       in,
			Output:      out,
			AssetID:     currency,
			Balance:     balances[currency],
			Chain:       int32(pb.CHAIN_OPBNB),
			BlockNumber: blockNumber,
			BlockHash:   blockHash,
			Private:     private,
			OracleKey:   oracleKey,
			ExtInfo:     string(extInfo),
			Date:        uint32(time.Now().Unix()),
			Line:        lines,
			SeatId:      baseData.DeskID,
			Payout:      payout,
		}
		message.Stream.Cast(bc.ServerID, &pb.StatisticsReq{
			UserID:   user.ID,
			PlayData: playData,
		})
	}

	// 记录 lastWin
	if user.Spin.LastWin == nil {
		user.Spin.LastWin = make(map[int32]float64)
	}
	user.Spin.LastWin[baseData.GameID] = output
	user.Spin.MarkDirty()
	return &pb.GameSpinResp{
		Code:        pb.SUCCESS,
		Payout:      float64(payout) / float64(lines),
		Input:       sub,
		Output:      output,
		Detail:      detail,
		Balance:     balances,
		CurBalance:  balances[currency],
		JackpotInfo: jackpotInfo,
		HashInfo: &pb.SpinHashVerify{
			GameID:      baseData.GameID,
			Private:     "0x" + private,
			BlockHash:   blockHash,
			BlockNumber: blockNumber,
			Seed:        strconv.FormatInt(seed, 10),
			Mode:        baseData.Mode,
		},
	}
}

func (uc *useCase) gmSpin(baseData BaseSpinData, ctx map[string]any, gameinfo *gsconf.GameInfoConf) (any, bool) {
	debugJackpot := false
	b := conf.Bool("debug.open", false)
	var spin any
	if !b {
		return spin, false
	}
	gmOddsLoad, ok := gmGameOdds.Load(strconv.Itoa(int(baseData.GameID)))
	if !ok {
		gmOddsLoad, ok = gmGameOdds.Load("")
	}
	if ok {
		gmOdds := float64(gmOddsLoad.(float64))
		if gmOdds == -3 {
			debugJackpot = true
		} else if gmOdds != 0 {
			lines := func() int32 {
				if gameinfo.Platform == "xking" {
					return igameKing.Line(baseData.GameID)
				}
				return igame.Line(baseData.GameID)
			}()
			bestDiff := math.MaxFloat64
			var num int = func() int {
				if gameinfo.Platform == "xking" {
					return 100
				}
				return 1000
			}()
			for i := 0; i < num; i++ {
				seed := int64(rand.Int())
				log.Warnf("gmOdds %v, seed %v", gmOdds, seed)
				var spin_ any = func() any {
					if gameinfo.Platform == "xking" {
						s, _ := igameKing.SpinData(baseData.GameID, baseData.Mode, seed, gameinfo.Tax, ctx)
						return s
					}
					s, _ := igame.Spin(baseData.GameID, baseData.Mode, seed, gameinfo.Tax)
					return s
				}()
				// spin_, _ := igame.Spin(baseData.GameID, baseData.Mode, seed, gameinfo.Tax)
				if gmOdds == -1 { //free
					spin1, ok := spin_.(basic.ISpin)
					if ok {
						if spin1.Tags()[0] == basic.EnumSpinTag.FREE {
							spin = spin_
							break
						}
					}
					spin2, ok := spin_.(basicKing.ISpin)
					if ok {
						if spin2.Tags()[0] == basic.EnumSpinTag.FREE {
							spin = spin_
							break
						}
					}
				} else if gmOdds == -2 { //spec
					spin1, ok := spin_.(basic.ISpin)
					if ok {
						if spin1.Tags()[0] == basic.EnumSpinTag.SPEC {
							spin = spin_
							break
						}
					}
					spin2, ok := spin_.(basicKing.ISpin)
					if ok {
						if spin2.Tags()[0] == basic.EnumSpinTag.SPEC {
							spin = spin_
							break
						}
					}
				} else if gmOdds > 0 {
					spin1, ok := spin_.(basic.ISpin)
					if ok {
						if spin1.Tags()[0] == basic.EnumSpinTag.FREE {
							continue
						}
						if spin1.Tags()[0] == basic.EnumSpinTag.SPEC {
							continue
						}
						out2 := float64(spin1.Payout()) / float64(lines)
						diff := math.Abs(out2 - gmOdds)
						if diff < bestDiff {
							bestDiff = diff
							spin = spin_
						}
					}
					spin2, ok := spin_.(basicKing.ISpin)
					if ok {
						if spin2.Tags()[0] == basic.EnumSpinTag.FREE {
							continue
						}
						if spin2.Tags()[0] == basic.EnumSpinTag.SPEC {
							continue
						}
						out2 := float64(spin2.Payout()) / float64(lines)
						diff := math.Abs(out2 - gmOdds)
						if diff < bestDiff {
							bestDiff = diff
							spin = spin_
						}
					}
				}
			}
		}
	}
	return spin, debugJackpot
}

func (uc *useCase) onGameSpinConfigReq(user *userdata.M, body *pb.GameSpinConfigReq, response func(*pb.GameSpinConfigResp, error)) {
	inputInfo := table.Find(func(iic *gsconf.InputInfoConf) bool {
		return iic.CurrencyID == body.Currency && iic.GameID == body.GameID
	})
	if inputInfo == nil {
		response(&pb.GameSpinConfigResp{
			Code: pb.PARAM_ERROR,
		}, nil)
		return
	}
	response(&pb.GameSpinConfigResp{
		Rule:  igame.SpinRule(body.GameID),
		Plans: inputInfo.ToPB(),
	}, nil)
}

func (uc *useCase) onGameSpinRecordReq(user *userdata.M, body *pb.GameSpinRecordReq, response func(*pb.GameSpinRecordResp, error)) {
	if table.Get[gsconf.GameInfoConf](body.GameID) == nil {
		response(&pb.GameSpinRecordResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	if body.Limit > 100 {
		response(&pb.GameSpinRecordResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	if body.GameSN == 0 {
		body.GameSN = math.MaxInt32
	}
	body.Endtime = max(body.Endtime, time.Now().Unix()-180*24*60*60, user.RegTime) // 只支持到180天之内
	if body.Endtime > body.Starttime {
		response(&pb.GameSpinRecordResp{Code: pb.SUCCESS}, nil)
		return
	}
	body.Limit = min(body.Limit, 100)
	var records []*struct {
		Private   string
		Public    string
		BlockHash string
		AssetID   int32
		SN        int32
		Input     float64
		Output    float64
		Mode      int32
		CreateAt  int64
	}
	err := chdb.Default().Table(mbi.TableSpinResult).
		Select("private", "block_hash", "asset_id", "input", "output", "sn", "create_at", "mode").
		Where("create_at >= ? AND create_at <= ? AND user_id = ? AND game_id = ? AND sn < ? AND asset_id = ?", body.Endtime, body.Starttime, user.ID, strconv.Itoa(int(body.GameID)), body.GameSN, body.AssetID).
		Order("create_at DESC").
		Limit(int(body.Limit)).
		Find(&records).Error
	if err != nil {
		log.Error(err)
		response(&pb.GameSpinRecordResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	// for _, item := range records {
	// 	item.Public, _ = web3.GetAddressFromPrivateKey(item.Private)
	// }
	resp := &pb.GameSpinRecordResp{Code: pb.SUCCESS, Records: make([]*pb.GameSpinRecord, 0, len(records))}
	for _, r := range records {
		resp.Records = append(resp.Records, &pb.GameSpinRecord{
			Private:      r.Private,
			Public:       "0x" + r.Private,
			CurrencyType: r.AssetID,
			GameSN:       r.SN,
			Bet:          r.Input,
			Win:          r.Output,
			Mode:         r.Mode,
			Time:         r.CreateAt,
		})
	}
	response(resp, nil)
}

func (uc *useCase) onGameSpinRecordCountReq(user *userdata.M, body *pb.GameSpinRecordCountReq, response func(*pb.GameSpinRecordCountResp, error)) {
	if table.Get[gsconf.GameInfoConf](body.GameID) == nil || (body.Starttime-body.Endtime) > 7*24*60*60 {
		response(&pb.GameSpinRecordCountResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	var count int64
	err := chdb.Default().Table(mbi.TableSpinResult).
		Select("private").
		Where("create_at >= ? AND create_at <= ? AND user_id = ? AND game_id = ? AND asset_id = ?", body.Endtime, body.Starttime, user.ID, strconv.Itoa(int(body.GameID)), body.AssetID).
		Order("create_at DESC").
		Count(&count).Error
	if err != nil {
		log.Error(err)
		response(&pb.GameSpinRecordCountResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	response(&pb.GameSpinRecordCountResp{Code: pb.SUCCESS, Count: int32(count)}, nil)
}

func (uc *useCase) onGameSpinRecordQueryReq(user *userdata.M, body *pb.GameSpinRecordQueryReq, response func(*pb.GameSpinRecordQueryResp, error)) {
	if table.Get[gsconf.GameInfoConf](body.GameID) == nil {
		response(&pb.GameSpinRecordQueryResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	body.Starttime = max(body.Starttime, time.Now().Unix()-180*24*60*60) // 只支持到180天之内
	body.Endtime = max(body.Endtime, time.Now().Unix())                  // 只支持到180天之内
	if body.Endtime < body.Starttime {
		response(&pb.GameSpinRecordQueryResp{Code: pb.SUCCESS}, nil)
		return
	}
	var record *struct {
		Private   string
		Public    string
		Mode      int32
		BlockHash string
		SN        int32
		AssetID   int32
		Input     float64
		Output    float64
		ExtInfo   string
		CreateAt  int64
	}
	err := chdb.Default().Table(mbi.TableSpinResult).
		Select("private", "block_hash", "input", "output", "ext_info", "sn", "create_at", "mode").
		Where("create_at >= ? AND create_at <= ? AND private = ?",
			body.Starttime, body.Endtime, body.Public).
		Order("create_at DESC").
		First(&record).Error
	if err != nil {
		log.Error(err)
		response(&pb.GameSpinRecordQueryResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	response(&pb.GameSpinRecordQueryResp{
		Code: pb.SUCCESS,
		Record: &pb.GameSpinRecord{
			Private:      record.Private,
			Public:       "0x" + record.Private,
			CurrencyType: record.AssetID,
			GameSN:       record.SN,
			Bet:          record.Input,
			Win:          record.Output,
			Time:         record.CreateAt,
			Mode:         record.Mode,
		},
	}, nil)
}

func (uc *useCase) onGamePGSpinRecordReq(user *userdata.M, body *pb.GamePGSpinRecordReq, response func(*pb.GamePGSpinRecordResp, error)) {
	gameInfo := table.Get[gsconf.GameInfoConf](body.GameID)
	if gameInfo == nil {
		response(&pb.GamePGSpinRecordResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	if gameInfo.Platform == "xking" {
		ctx := map[string]any{}
		err := json.Unmarshal([]byte(body.Ctx), &ctx)
		if err != nil {
			log.Errorf("king game GamePGSpinRecordReq unmarshal error: %v", err)
			response(&pb.GamePGSpinRecordResp{Code: pb.SERVER_ERROR}, nil)
			return
		}
		kingGameID := igameKing.KingGameId(body.GameID)
		ctx["gameId"] = kingGameID
		ctx["gid"] = kingGameID
		apiurl := ctx["apiurl"].(string)
		delete(ctx, "apiurl")
		apiurl = basicKing.ApiBaseURL + apiurl
		respBytes, err := basicKing.SendKingHtttpPost(ctx, apiurl)
		log.Infof("king game GamePGSpinRecordReq respBytes: %v, ctx: %v, apiurl: %v", string(respBytes), ctx, apiurl)
		if err != nil {
			ctxStr, _ := json.Marshal(ctx)
			log.Errorf("king game GamePGSpinRecordReq send request ctx: %v, apiurl: %v, error: %v", string(ctxStr), apiurl, err)
			response(&pb.GamePGSpinRecordResp{Code: pb.SERVER_ERROR}, nil)
			return
		}
		response(&pb.GamePGSpinRecordResp{Code: pb.SUCCESS, Data: respBytes}, nil)
		return
	}
	if body.Limit > 100 {
		response(&pb.GamePGSpinRecordResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	body.Endtime = max(body.Endtime, time.Now().Unix()-180*24*60*60, user.RegTime) // 只支持到180天之内
	if body.Endtime < body.Starttime {
		response(&pb.GamePGSpinRecordResp{Code: pb.SUCCESS}, nil)
		return
	}
	body.Limit = min(body.Limit, 100)

	// 获取总记录数和总和
	var total int64
	var totalInput, totalOutput float64
	err := chdb.Default().Table(mbi.TableSpinResult).
		Select("COUNT(*) as total, SUM(input) as total_input, SUM(output) as total_output").
		Where("create_at >= ? AND create_at <= ? AND user_id = ? AND game_id = ?",
			body.Starttime, body.Endtime, user.ID, strconv.Itoa(int(body.GameID))).
		Row().Scan(&total, &totalInput, &totalOutput)
	if err != nil {
		log.Error(err)
		response(&pb.GamePGSpinRecordResp{Code: pb.SERVER_ERROR}, err)
		return
	}

	var records []*struct {
		Private   string
		Public    string
		Mode      int32
		BlockHash string
		SN        int32
		AssetID   int32
		Input     float64
		Output    float64
		ExtInfo   string
		CreateAt  int64
	}
	err = chdb.Default().Table(mbi.TableSpinResult).
		Select("private", "block_hash", "input", "output", "ext_info", "sn", "create_at").
		Where("create_at >= ? AND create_at <= ? AND user_id = ? AND game_id = ?",
			body.Starttime, body.Endtime, user.ID, strconv.Itoa(int(body.GameID))).
		Order("create_at DESC").
		Offset(int((body.Offset - 1) * body.Limit)).
		Limit(int(body.Limit)).
		Find(&records).Error
	if err != nil {
		log.Error(err)
		response(&pb.GamePGSpinRecordResp{Code: pb.SERVER_ERROR}, err)
		return
	}

	list := make([]map[string]any, 0, len(records))
	var input float64 = 0
	var output float64 = 0
	for _, r := range records {
		input += r.Input
		output += r.Output
		ctx := map[string]any{}
		log.Info("r.ExtInfo:", r.ExtInfo)
		err := json.Unmarshal(utils.DecompressFromBase64(r.ExtInfo), &ctx)
		if err != nil {
			continue
		}
		logstr, ok := ctx["log"].(string)
		if !ok {
			continue
		}
		log := map[string]any{}
		log["private"] = "0x" + r.Private
		err = json.Unmarshal([]byte(logstr), &log)
		if err != nil {
			continue
		}
		list = append(list, log)
	}
	data, _ := json.Marshal(map[string]any{
		"code": 0,
		"msg":  nil,
		"data": map[string]any{
			"list":        list,
			"total":       total, //总记录数
			"page":        body.Offset,
			"pageSize":    body.Limit,
			"totalBet":    totalInput,               //总输入
			"totalPayout": totalOutput - totalInput, //总利润
			"lastTable":   0,
			"lastOffset":  0,
			"merId":       0,
			"curSymbol":   nil,
		},
	})
	response(&pb.GamePGSpinRecordResp{
		Code: pb.SUCCESS,
		Data: data,
	}, nil)
}

func (uc *useCase) onGamePGSummaryReq(user *userdata.M, body *pb.GameSpinPGSummaryReq, response func(*pb.GameSpinPGSummaryResp, error)) {
	if table.Get[gsconf.GameInfoConf](body.GameID) == nil {
		response(&pb.GameSpinPGSummaryResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	body.Endtime = max(body.Endtime, time.Now().Unix()-180*24*60*60, user.RegTime) // 只支持到180天之内
	if body.Endtime < body.Starttime {
		response(&pb.GameSpinPGSummaryResp{Code: pb.SUCCESS}, nil)
		return
	}
	var records []*struct {
		Number int32
		Input  float64
		Output float64
	}
	err := chdb.Default().Table(mbi.TableSpinResult).
		Select("COUNT(*) AS number", "SUM(`input`) AS input", "SUM(`output`) AS output").
		Where("create_at >= ? AND create_at <= ? AND user_id = ? AND game_id = ? AND asset_id = ?", body.Starttime, body.Endtime, user.ID, strconv.Itoa(int(body.GameID)), body.AssetID).
		Find(&records).Error
	if err != nil {
		log.Error(err)
		response(&pb.GameSpinPGSummaryResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	lbid := (body.GameID % 1000000) * 10000000
	data, _ := json.Marshal(map[string]any{
		"dt": map[string]any{
			"lut": (time.Now().Unix() / 24 * 60 * 60) * 24 * 60 * 60 * 1000, //今日零点时间戳
			"bs": map[string]any{
				"bc":    records[0].Number,
				"btba":  records[0].Input,
				"btwla": records[0].Output,
				"gid":   body.GameID % 1000000,
				"lbid":  lbid,
			},
		},
		"err": nil,
	})
	response(&pb.GameSpinPGSummaryResp{
		Code: pb.SUCCESS,
		Data: data,
	}, nil)
}

// 结果验证
func (uc *useCase) onGameSpinPayoutReq(user *userdata.M, body *pb.GameSpinPayoutReq, response func(*pb.GameSpinPayoutResp, error)) {
	n, err := strconv.ParseInt(body.Seed, 10, 64)
	if err != nil {
		response(&pb.GameSpinPayoutResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	gameInfo := table.Get[gsconf.GameInfoConf](body.GameID)
	if gameInfo == nil {
		response(&pb.GameSpinPayoutResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	log.Infof("verify gameId %v, uid %d, mode %v, seed %d, oracleKey %v", body.GameID, user.ID, body.Mode, n, gameInfo.Tax)
	var payout int32
	var lines int32
	if gameInfo.Platform == "xking" {
		payout = igameKing.Payout(body.GameID, body.Mode, n, gameInfo.Tax, nil)
		lines = igameKing.Line(body.GameID)
	} else {
		spin, _ := igame.Spin(body.GameID, body.Mode, n, gameInfo.Tax)
		lines = igame.Line(body.GameID)
		payout = spin.Payout()
	}
	response(&pb.GameSpinPayoutResp{
		Code:   pb.SUCCESS,
		Payout: payout,
		Line:   lines,
		X:      float64(payout) / float64(lines),
	}, nil)
}

func (uc *useCase) onUpdateBlockHashMsg(body *pb.UpdateBlockHashMsg) {
	for _, item := range body.Updates {
		uc.hash.Store(item.Chain, item)
	}
}

func (uc *useCase) onGameSpinPGRecordDetailReq(body *pb.GameSpinPGRecordDetailReq, response func(*pb.GameSpinPGRecordDetailResp, error)) {
	ctx := map[string]any{}
	err := json.Unmarshal([]byte(body.Ctx), &ctx)
	if err != nil {
		log.Errorf("king game GameSpinPGRecordDetailReq unmarshal error: %v", err)
		response(&pb.GameSpinPGRecordDetailResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	if ctx["gameId"] != nil {
		gameIdFloat, ok := ctx["gameId"].(float64)
		if !ok {
			gameIdStr, ok := ctx["gameId"].(string)
			if ok {
				gameIdFloat_, err := strconv.ParseFloat(gameIdStr, 64)
				if err == nil {
					gameIdFloat = gameIdFloat_
				}
			}
		}
		gameId := int32(gameIdFloat)
		kingGameID := igameKing.KingGameId(gameId)
		ctx["gameId"] = kingGameID
	}
	apiurl := ctx["apiurl"].(string)
	delete(ctx, "apiurl")
	apiurl = basicKing.ApiBaseURL + apiurl
	respBytes, err := basicKing.SendKingHtttpPost(ctx, apiurl)
	if err != nil {
		response(&pb.GameSpinPGRecordDetailResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	response(&pb.GameSpinPGRecordDetailResp{Code: pb.SUCCESS, Data: string(respBytes)}, nil)
}

var gmGameOdds sync.Map = sync.Map{}

func (uc *useCase) onGMSetupOddsToGameReq(body *pb.GMSetupOddsToGameReq, response func(*pb.GMSetupOddsToGameResp, error)) {
	list := []*pb.GMGameOdds{}
	if body.Data != nil {
		gmGameOdds.Store(body.Data.GameID, body.Data.Value)
	}
	gmGameOdds.Range(func(key, value any) bool {
		list = append(list, &pb.GMGameOdds{
			GameID: key.(int32),
			Value:  value.(float64),
		})
		return true
	})
	response(&pb.GMSetupOddsToGameResp{Code: pb.SUCCESS, List: list}, nil)
}

func (uc *useCase) onGameBTAUserSettingReq(user *userdata.M, body *pb.GameBTAUserSettingReq, response func(*pb.GameBTAUserSettingResp, error)) {
	if user.Settings.Setting == nil {
		user.Settings.Setting = make(map[int32]string)
	}
	// 先读取原有设置
	setting := map[string]any{}
	if old, ok := user.Settings.Setting[body.GameID]; ok && old != "" {
		_ = json.Unmarshal([]byte(old), &setting)
	}
	// 覆盖原有字段
	if body.Language != "" {
		setting["language"] = body.Language
	}
	if body.SmallGameType != "" {
		setting["smallGameType"] = body.SmallGameType
	}
	settingBytes, _ := json.Marshal(setting)
	user.Settings.Setting[body.GameID] = string(settingBytes)
	user.Settings.MarkDirty()
	log.Info("user.Settings.Setting[body.GameID]", user.Settings.Setting[body.GameID])
	response(&pb.GameBTAUserSettingResp{Code: pb.SUCCESS, Setting: user.Settings.Setting[body.GameID]}, nil)
}

func (uc *useCase) onGameBTASpinReq(user *userdata.M, body *pb.GameBTASpinReq, response func(*pb.GameSpinResp, error)) {
	inputInfo := table.Find[gsconf.InputInfoConf](func(iic *gsconf.InputInfoConf) bool {
		return iic.CurrencyID == body.Currency && iic.GameID == body.GameID
	})
	if inputInfo == nil {
		response(&pb.GameSpinResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	// 1. 参数校验
	if user == nil || user.ID <= 0 {
		response(&pb.GameSpinResp{Code: pb.ACCOUNT_NOT_FOUND}, nil)
		return
	}
	if body.Input <= 0 {
		response(&pb.GameSpinResp{Code: pb.PARAM_ERROR}, nil)
		return
	}

	// 2. 资产校验
	bc, err := cache.QueryUserBasicInfo(user.ID)
	if err != nil {
		response(&pb.GameSpinResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	resp, err := message.Request[pb.AssetBalanceResp](bc.ServerID, &pb.AssetBalanceReq{
		UserID: user.ID,
	})
	if err != nil {
		response(&pb.GameSpinResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	balance := resp.Balance[body.Currency]
	if balance < body.Input {
		response(&pb.GameSpinResp{Code: pb.BALANCE_NOT_ENOUGH}, nil)
		return
	}
	// 3. 组装上下文
	ctx := map[string]any{}
	if body.Ctx != "" {
		_ = json.Unmarshal([]byte(body.Ctx), &ctx)
	}
	ctx["input"] = body.Input
	ctx["gamble"] = body.Gamble
	ctx["mode"] = body.Mode
	ctx["balance"] = balance
	ctx["currency"] = body.Currency
	ctx["language"] = body.Language
	if body.GameID == 400009 {
		ctx["smallGameType"] = body.SmallGameType
		ctx["step"] = body.Step
	}

	if body.GameID == 400128 {
		if ibetChange, ok := ctx["ibetChange"].(string); ok {
			user.Spin.IbetsChange[body.GameID] = ibetChange
			user.Spin.MarkDirty()
		}
	}

	// 4. 调用 spin 逻辑
	respSpin := uc.gameSpin(user, BaseSpinData{
		GameID:   body.GameID,
		Mode:     body.Mode,
		DeskID:   body.DeskID,
		Currency: body.Currency,
		Input:    body.Input,
	}, ctx)

	var detail []byte
	var res1 = map[string]any{}
	err = json.Unmarshal([]byte(respSpin.Detail), &res1)
	if err != nil {
		var res2 = []map[string]any{}
		err = json.Unmarshal([]byte(respSpin.Detail), &res2)
		if err == nil {
			res2[0]["JackpotInfo"] = respSpin.JackpotInfo
			res2[0]["HashInfo"] = respSpin.HashInfo
			detail, _ = json.Marshal(res2)
		}
	} else {
		res1["JackpotInfo"] = respSpin.JackpotInfo
		res1["HashInfo"] = respSpin.HashInfo
		detail, _ = json.Marshal(res1)
	}
	respSpin.Detail = string(detail)
	if body.GameID == 400009 {
		json.Unmarshal([]byte(respSpin.Detail), &res1)
		detail, _ = json.Marshal(res1["start"])
		user.SmallGame.SpinData[body.GameID] = string(detail)
		user.SmallGame.MarkDirty()
	}
	// 5. 返回结果
	response(respSpin, nil)
}

func (uc *useCase) onSmallGameBtaReq(user *userdata.M, body *pb.SmallGameBtaReq, response func(*pb.SmallGameBtaResp, error)) {
	// 1. 用户合法性校验
	if user == nil || user.ID <= 0 {
		response(&pb.SmallGameBtaResp{Code: pb.ACCOUNT_NOT_FOUND, Detail: "用户非法"}, nil)
		return
	}
	if user.SmallGame.SpinData == nil {
		response(&pb.SmallGameBtaResp{Code: pb.PARAM_ERROR, Detail: "用户数据非法"}, nil)
		return
	}

	// 2. 获取小游戏类型和 lastwin
	var smallGameType string
	if user.Settings.Setting != nil {
		if v, ok := user.Settings.Setting[body.GameID]; ok && v != "" {
			setting := map[string]any{}
			_ = json.Unmarshal([]byte(v), &setting)
			if t, ok := setting["smallGameType"].(string); ok {
				smallGameType = t
			}
		}
	}
	if smallGameType == "" {
		response(&pb.SmallGameBtaResp{Code: pb.PARAM_ERROR, Detail: "未设置小游戏类型"}, nil)
		return
	}
	lastwin := 0.0
	if user.Spin.LastWin != nil {
		lastwin = user.Spin.LastWin[body.GameID]
	}
	if lastwin <= 0 {
		response(&pb.SmallGameBtaResp{Code: pb.PARAM_ERROR, Detail: "lastwin非法"}, nil)
		return
	}
	// 3. 处理小游戏逻辑
	var spinData map[string]any
	json.Unmarshal([]byte(user.SmallGame.SpinData[body.GameID]), &spinData)
	var smallGameStatus []string
	var win float64
	av := rand.New(rand.NewSource(time.Now().UnixNano())).Intn(38) + 1
	switch smallGameType {
	case "dealer":
		if rand.Float64() < 0.5 {
			smallGameStatus = []string{"win", "2"}
		} else {
			smallGameStatus = []string{"lose", "0"}
		}
		// 进入小游戏
		if body.Choice == "askDouble" {
			subGameInfo := map[string]any{
				"add": map[string]any{
					"carddealer": av,
					"openDealer": 1,
				},
				"attempt":        0,
				"attemptResult":  0,
				"av":             []int{av, 0, 0, 0, 0},
				"category":       "Double",
				"curWin":         lastwin,
				"dblhalf":        0,
				"halfWin":        0,
				"onlyToBD":       nil,
				"openDealerCard": 1,
				"paidWin":        -1,
				"prevWin":        lastwin,
				"rule":           "color",
				"sendRestore":    nil,
				"set": []any{
					"dealer",
					5,
					100000,
					[]string{"openCardYes"},
				},
				"startWin":   lastwin,
				"type":       "dealer",
				"userChoice": "",
				"winLevel":   0,
			}

			spinData["gs"].(map[string]any)["phaseCur"] = "Doubledealer"
			spinData["gs"].(map[string]any)["phaseNext"] = "toDouble"
			if spinData["gs"].(map[string]any)["subGameInfo"] != nil && len(spinData["gs"].(map[string]any)["subGameInfo"].([]any)) > 0 {
				spinData["gs"].(map[string]any)["subGameInfo"] = append(spinData["gs"].(map[string]any)["subGameInfo"].([]any), subGameInfo)
			} else {
				spinData["gs"].(map[string]any)["subGameInfo"] = []any{subGameInfo}
			}
			b, _ := json.Marshal(spinData)
			// 更新数据
			user.SmallGame.SpinData[body.GameID] = string(b)
			user.SmallGame.MarkDirty()
		} else if body.Choice == "askDoubleBackToGame" {
			smallGameStatus = []string{"back", "0"}
			subGameInfo := map[string]any{
				"add": map[string]any{
					"carddealer": av,
					"openDealer": 1,
				},
				"attempt":        0,
				"attemptResult":  0,
				"av":             []int{av, 0, 0, 0, 0},
				"category":       "Double",
				"curWin":         lastwin,
				"dblhalf":        0,
				"halfWin":        0,
				"onlyToBD":       nil,
				"openDealerCard": 1,
				"paidWin":        -1,
				"prevWin":        lastwin,
				"rule":           "color",
				"sendRestore":    nil,
				"set": []any{
					"dealer",
					5,
					100000,
					[]string{"openCardYes"},
				},
				"startWin":   lastwin,
				"type":       "dealer",
				"userChoice": "BackToGame",
				"winLevel":   0,
			}

			spinData["gs"].(map[string]any)["phaseCur"] = "Doubleredblack"
			spinData["gs"].(map[string]any)["phaseNext"] = "toDouble"
			spinData["gs"].(map[string]any)["subGameInfo"] = []any{subGameInfo}
			b, _ := json.Marshal(spinData)
			// 更新数据
			user.SmallGame.SpinData[body.GameID] = string(b)
			user.SmallGame.MarkDirty()
		} else if body.Choice != "askDouble" && body.Choice != "askDoubleBackToGame" {
			imul, _ := strconv.Atoi(smallGameStatus[1])
			win = lastwin * float64(imul)
			user.SmallGame.CurWin[body.GameID] = win
			user.SmallGame.MarkDirty()
			gs := spinData["gs"].(map[string]any)
			subGameInfo := gs["subGameInfo"].([]any)[len(gs["subGameInfo"].([]any))-1].(map[string]any)
			subGameInfo["av"] = []int{av, 0, 0, 0, 0}
			if subGameInfo["attempt"].(float64) >= 0 {
				subGameInfo["add"] = map[string]any{
					"carddealer": av,
					"openDealer": 1,
				}
			}
			choiceIndex, _ := strconv.Atoi(body.Choice)
			subGameInfo = BuildSubGameDealerInfo(win, smallGameStatus[0], choiceIndex, subGameInfo)
			if smallGameStatus[0] == "win" {
				gs["phaseCur"] = "Doubledealer"
				gs["phaseNext"] = "toDouble"
				b, _ := json.Marshal(spinData)
				// 更新数据
				user.SmallGame.SpinData[body.GameID] = string(b)
				user.SmallGame.MarkDirty()
			} else {
				gs["phaseCur"] = "Doubledealer_finished"
				gs["phaseNext"] = "toPaid"
				// 更新数据
				delete(user.SmallGame.SpinData, body.GameID)
				user.SmallGame.MarkDirty()

			}
			gs["curWin"] = win
			gs["subGameInfo"] = []any{subGameInfo}
		}
	case "redblack":
		if body.Choice == "askDouble" {
			smallGameStatus = []string{"init", "0"}
			subGameInfo := map[string]any{
				"category":      "Double",
				"type":          "redblack",
				"startWin":      lastwin,
				"prevWin":       lastwin,
				"curWin":        lastwin,
				"paidWin":       -1,
				"attempt":       0,
				"av":            []any{},
				"attemptResult": 0,
				"winLevel":      0,
				"rule":          "colorsuit",
				"add":           map[string]any{},
				"onlyToBD":      nil,
				"set":           []any{"redblack", 5, 100000},
				"dblhalf":       0,
				"halfWin":       0,
				"userChoice":    "",
				"sendRestore":   nil,
			}

			spinData["gs"].(map[string]any)["phaseCur"] = "Doubleredblack"
			spinData["gs"].(map[string]any)["phaseNext"] = "toDouble"
			if spinData["gs"].(map[string]any)["subGameInfo"] != nil && len(spinData["gs"].(map[string]any)["subGameInfo"].([]any)) > 0 {
				spinData["gs"].(map[string]any)["subGameInfo"] = append(spinData["gs"].(map[string]any)["subGameInfo"].([]any), subGameInfo)
			} else {
				spinData["gs"].(map[string]any)["subGameInfo"] = []any{subGameInfo}
			}
			b, _ := json.Marshal(spinData)
			// 更新数据
			user.SmallGame.SpinData[body.GameID] = string(b)
			user.SmallGame.MarkDirty()
		} else if body.Choice == "askDoubleBackToGame" {
			smallGameStatus = []string{"back", "0"}
			subGameInfo := map[string]any{
				"category":      "Double",
				"type":          "redblack",
				"startWin":      lastwin,
				"prevWin":       lastwin,
				"curWin":        lastwin,
				"paidWin":       -1,
				"attempt":       0,
				"av":            []any{},
				"attemptResult": 0,
				"winLevel":      0,
				"rule":          "colorsuit",
				"add":           map[string]any{},
				"onlyToBD":      nil,
				"set":           []any{"redblack", 5, 100000},
				"dblhalf":       0,
				"halfWin":       0,
				"userChoice":    "BackToGame",
				"sendRestore":   nil,
			}

			spinData["gs"].(map[string]any)["phaseCur"] = "Doubleredblack"
			spinData["gs"].(map[string]any)["phaseNext"] = "toDouble"
			spinData["gs"].(map[string]any)["subGameInfo"] = []any{subGameInfo}
			b, _ := json.Marshal(spinData)
			// 更新数据
			user.SmallGame.SpinData[body.GameID] = string(b)
			user.SmallGame.MarkDirty()
		} else if body.Choice != "askDouble" && body.Choice != "askDoubleBackToGame" {
			r := rand.Float64()
			switch body.Choice {
			case "Red":
				if r < 0.25 {
					smallGameStatus = []string{"win", "2"}
				} else {
					smallGameStatus = []string{"lose", "0"}
				}
			case "Black":
				if r < 0.25 {
					smallGameStatus = []string{"win", "2"}
				} else {
					smallGameStatus = []string{"lose", "0"}
				}
			case "Spade":
				if r < 0.125 {
					smallGameStatus = []string{"win", "4"}
				} else {
					smallGameStatus = []string{"lose", "0"}
				}
			case "Club":
				if r < 0.125 {
					smallGameStatus = []string{"win", "4"}
				} else {
					smallGameStatus = []string{"lose", "0"}
				}
			case "Heart":
				if r < 0.125 {
					smallGameStatus = []string{"win", "4"}
				} else {
					smallGameStatus = []string{"lose", "0"}
				}
			case "Diamond":
				if r < 0.125 {
					smallGameStatus = []string{"win", "4"}
				} else {
					smallGameStatus = []string{"lose", "0"}
				}
			}

			imul, _ := strconv.Atoi(smallGameStatus[1])
			win = lastwin * float64(imul)
			user.SmallGame.CurWin[body.GameID] = win
			user.SmallGame.MarkDirty()
			gs := spinData["gs"].(map[string]any)
			gs["last5cards"] = []int{1, 48, 3, 26, 45}
			subGameInfo := gs["subGameInfo"].([]any)[len(gs["subGameInfo"].([]any))-1].(map[string]any)
			subGameInfo = BuildSubGameRedBlackInfo(win, smallGameStatus[0], body.Choice, subGameInfo)
			if smallGameStatus[0] == "win" {
				gs["phaseCur"] = "Doubleredblack"
				gs["phaseNext"] = "toDouble"
				b, _ := json.Marshal(spinData)
				// 更新数据
				user.SmallGame.SpinData[body.GameID] = string(b)
				user.SmallGame.MarkDirty()
			} else {
				gs["phaseCur"] = "Doubledealer_finished"
				gs["phaseNext"] = "toPaid"
				// 更新数据
				delete(user.SmallGame.SpinData, body.GameID)
				user.SmallGame.MarkDirty()
			}
			gs["curWin"] = win
			gs["subGameInfo"] = []any{subGameInfo}
		}
	default:
		spinData["gs"].(map[string]any)["phaseCur"] = "basedeal"
		spinData["gs"].(map[string]any)["phaseNext"] = "toPaid"
	}

	// 4. 更新游戏数据
	user.SmallGame.Status[body.GameID] = strings.Join(smallGameStatus, ",")
	user.SmallGame.Output[body.GameID] = win
	b, _ := json.Marshal(spinData)
	user.SmallGame.SpinData[body.GameID] = string(b)
	user.SmallGame.MarkDirty()
	// 6. 返回结果
	respDetail := map[string]any{
		"type":    smallGameType,
		"lastwin": win,
		"result":  string(b),
	}
	detailBytes, _ := json.Marshal(respDetail)
	response(&pb.SmallGameBtaResp{Code: pb.SUCCESS, Detail: string(detailBytes)}, nil)
}

func (uc *useCase) onGetSmallGameBtaFinishReq(user *userdata.M, body *pb.GetSmallGameBtaFinishReq, response func(*pb.GetSmallGameBtaFinishResp, error)) {
	inputInfo := table.Find[gsconf.InputInfoConf](func(iic *gsconf.InputInfoConf) bool {
		return iic.CurrencyID == body.Currency && iic.GameID == body.GameID
	})
	if inputInfo == nil {
		response(&pb.GetSmallGameBtaFinishResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	// 1. 用户合法性校验
	if user == nil || user.ID <= 0 {
		response(&pb.GetSmallGameBtaFinishResp{Code: pb.ACCOUNT_NOT_FOUND, Data: "用户非法"}, nil)
		return
	}

	bc, err := cache.QueryUserBasicInfo(user.ID)
	if err != nil {
		response(&pb.GetSmallGameBtaFinishResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	resp, err := message.Request[pb.AssetBalanceResp](bc.ServerID, &pb.AssetBalanceReq{
		UserID: user.ID,
	})
	if err != nil {
		response(&pb.GetSmallGameBtaFinishResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	balance := resp.Balance[body.Currency]
	if balance < body.Input {
		response(&pb.GetSmallGameBtaFinishResp{Code: pb.BALANCE_NOT_ENOUGH}, nil)
		return
	}

	input := body.Input
	output := user.SmallGame.Output[body.GameID]
	settingS := user.Settings.Setting[body.GameID]
	lastwin := user.Spin.LastWin[body.GameID]
	settingMap := map[string]any{}
	json.Unmarshal([]byte(settingS), &settingMap)

	if settingMap["smallGameType"] != nil && settingMap["smallGameType"] != "" {
		currency := inputInfo.CurrencyID
		balance := resp.Balance[currency]
		curwin := user.SmallGame.CurWin[body.GameID]

		// 检查余额是否足够扣除 lastwin
		if balance < lastwin {
			response(&pb.GetSmallGameBtaFinishResp{Code: pb.BALANCE_NOT_ENOUGH, Data: "余额不足"}, nil)
			return
		}

		// 构建资产变更请求
		var changeAssets []*pb.IDValFloat
		changeAssets = []*pb.IDValFloat{
			{ID: int64(currency), Value: curwin - lastwin},
		}
		// 执行资产变更
		_, err = message.Request[pb.ChangeAssetResp](bc.ServerID, &pb.ChangeAssetReq{
			UserID: user.ID,
			Cause:  "smallgame_bta",
			Assets: changeAssets,
		})
		if err != nil {
			response(&pb.GetSmallGameBtaFinishResp{Code: pb.SERVER_ERROR, Data: "资产变更失败"}, nil)
			return
		}
	}

	res := map[string]any{
		"useracc": map[string]any{
			"amount":       balance - input + output,
			"currency":     "",
			"altcurr":      "",
			"currencyUnit": 1,
			"symbol_first": "0",
		},
		"ss": map[string]any{},
		"gs": map[string]any{
			"placedbet":        input,
			"jpsWinPaid_cents": 0,
			"phaseCur":         "finished",
			"phaseNext":        "toIdle",
			"curWin":           output - input,
			"dramshow":         nil,
			"historyId":        19606469010,
		},
		"newOpenGames": []any{},
		"info_adv_games": map[string]any{
			"list": []any{},
			"hint": 0,
			"nnew": 0,
		},
		"uservars": map[string]any{
			"language":     settingMap["language"],
			"doublePrefer": settingMap["smallGameType"],
		},
		"maxBetPerGame_cents":   nil,
		"betAssortment":         []any{1, 2, 3, 4, 5, 6, 7, 10, 15, 20, 25, 40, 50, 75, 100, 125},
		"denomAssortment_cents": []any{1},
		"minBetPerGame_cents":   nil,
		"winValidation": map[string]any{
			"needcheck":                    false,
			"winlimit_fictiveRotate_gcurr": 25000000,
			"remaintime":                   86398317,
			"period":                       ********,
			"isApproved":                   false,
			"isNotApproved":                false,
			"isWaitApprove":                false,
		},
	}
	b, _ := json.Marshal(res)
	response(&pb.GetSmallGameBtaFinishResp{Code: pb.SUCCESS, Data: string(b)}, nil)
}

func (uc *useCase) onGetUserSettingReq(user *userdata.M, body *pb.GetUserSettingReq, response func(*pb.GetUserSettingResp, error)) {
	// 1. 用户合法性校验
	if user == nil || user.ID <= 0 {
		response(&pb.GetUserSettingResp{Code: pb.ACCOUNT_NOT_FOUND, Data: "用户非法"}, nil)
		return
	}
	// 2. 获取用户设置状态
	setting := ""
	if user.Settings.Setting != nil {
		setting = user.Settings.Setting[body.GameID]
		if setting == "" {
			setting = "{}"
		}
	}
	response(&pb.GetUserSettingResp{Code: pb.SUCCESS, Data: setting}, nil)
}
