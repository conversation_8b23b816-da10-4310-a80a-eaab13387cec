package spin

import (
	"encoding/json"
	"s2/modules/game/userops/userdata"
	"s2/pb"
	"strings"
	"testing"
)

func TestSmallGameIntegration(t *testing.T) {
	// 创建测试用户数据
	user := userdata.New()
	user.ID = 12345

	// 初始化用户数据
	if user.SmallGame.SpinData == nil {
		user.SmallGame.SpinData = make(map[int32]string)
	}
	if user.SmallGame.CurWin == nil {
		user.SmallGame.CurWin = make(map[int32]float64)
	}
	if user.Spin.LastWin == nil {
		user.Spin.LastWin = make(map[int32]float64)
	}

	gameID := int32(1001)
	user.Spin.LastWin[gameID] = 100.0

	// 创建初始 spinData
	spinData := map[string]any{
		"gs": map[string]any{
			"phaseCur":    "basedeal",
			"phaseNext":   "toPaid",
			"subGameInfo": []any{},
		},
	}
	spinDataBytes, _ := json.Marshal(spinData)
	user.SmallGame.SpinData[gameID] = string(spinDataBytes)

	// 创建适配器
	adapter := NewSmallGameAdapter()

	// 测试进入双倍游戏
	body := &pb.SmallGameBtaReq{
		GameID: gameID,
		Choice: "askDouble",
	}

	updatedSpinData, status, win, err := adapter.ProcessSmallGameRequest(
		user, body, "dealer", 100.0, spinData,
	)

	if err != nil {
		t.Fatalf("处理 askDouble 请求失败: %v", err)
	}

	if len(status) != 2 {
		t.Errorf("状态格式错误，期望长度2，实际长度: %d", len(status))
	}

	if updatedSpinData == nil {
		t.Error("更新的 spinData 不应该为空")
	}

	t.Logf("状态: %v, 赢取: %.2f", status, win)

	// 验证 spinData 结构
	gs, ok := updatedSpinData["gs"].(map[string]any)
	if !ok {
		t.Error("gs 字段应该存在且为 map")
	}

	if phaseCur, ok := gs["phaseCur"].(string); !ok || phaseCur == "" {
		t.Errorf("phaseCur 应该被设置，实际值: %v", gs["phaseCur"])
	}

	if subGameInfo, ok := gs["subGameInfo"].([]any); !ok || len(subGameInfo) == 0 {
		t.Errorf("subGameInfo 应该被设置，实际值: %v", gs["subGameInfo"])
	}

	t.Logf("集成测试通过")
}

func TestSmallGameAdapterChoiceMapping(t *testing.T) {
	adapter := NewSmallGameAdapter()

	testCases := []struct {
		choice       string
		currentState string
		expected     string
	}{
		{"askDouble", "Init", "askDouble"},
		{"askDouble", "DoubleEntry", "continue"},
		{"askDoubleBackToGame", "DoubleEntry", "askDoubleBackToGame"},
		{"1", "Doubledealer", "win"}, // 这个会根据随机结果变化
		{"collect", "DoubleWin", "collect"},
	}

	for _, tc := range testCases {
		result := adapter.MapChoiceToAction(tc.choice, tc.currentState)

		// 对于数字选择，结果应该是 "win" 或 "lose"
		if tc.choice >= "1" && tc.choice <= "4" {
			if result != "win" && result != "lose" {
				t.Errorf("数字选择 %s 应该返回 win 或 lose，实际返回: %s", tc.choice, result)
			}
		} else if result != tc.expected {
			t.Errorf("选择 %s 在状态 %s 下应该映射到 %s，实际映射到: %s",
				tc.choice, tc.currentState, tc.expected, result)
		}
	}
}

func TestSmallGameAdapterCompatibility(t *testing.T) {
	// 测试与现有格式的兼容性
	adapter := NewSmallGameAdapter()

	// 模拟链表响应
	chainResponse := map[string]any{
		"state": "Doubledealer",
		"data": map[string]any{
			"curWin":     200.0,
			"av":         []int{25, 0, 0, 0, 0},
			"carddealer": 25,
			"openDealer": 1,
		},
		"actions":  []string{"win", "lose", "back"},
		"terminal": false,
	}

	originalSpinData := map[string]any{
		"gs": map[string]any{
			"phaseCur":    "basedeal",
			"phaseNext":   "toPaid",
			"subGameInfo": []any{},
		},
	}

	status, win, updatedSpinData, err := adapter.convertToLegacyFormat(
		chainResponse, originalSpinData, "askDouble",
	)

	if err != nil {
		t.Fatalf("格式转换失败: %v", err)
	}

	if len(status) != 2 {
		t.Errorf("状态格式错误，期望长度2，实际长度: %d", len(status))
	}

	if updatedSpinData == nil {
		t.Error("更新的 spinData 不应该为空")
	}

	// 验证 subGameInfo 结构
	gs := updatedSpinData["gs"].(map[string]any)
	subGameInfo := gs["subGameInfo"].([]any)

	if len(subGameInfo) == 0 {
		t.Error("subGameInfo 应该包含至少一个元素")
	}

	firstSubGame := subGameInfo[0].(map[string]any)

	// 验证必要字段
	requiredFields := []string{"category", "type", "curWin", "av", "rule"}
	for _, field := range requiredFields {
		if _, exists := firstSubGame[field]; !exists {
			t.Errorf("subGameInfo 缺少必要字段: %s", field)
		}
	}

	t.Logf("兼容性测试通过，状态: %v, 赢取: %.2f", status, win)
}

func TestSmallGameAdapterErrorHandling(t *testing.T) {
	adapter := NewSmallGameAdapter()

	// 测试空用户数据
	_, _, _, err := adapter.ProcessSmallGameRequest(
		nil, &pb.SmallGameBtaReq{GameID: 1001, Choice: "askDouble"},
		"dealer", 100.0, map[string]any{},
	)

	if err == nil {
		t.Error("空用户数据应该返回错误")
	}

	// 测试无效的链表响应
	invalidResponse := map[string]any{
		"invalid": "data",
	}

	_, _, _, err = adapter.convertToLegacyFormat(
		invalidResponse, map[string]any{}, "askDouble",
	)

	if err == nil {
		t.Error("无效响应应该返回错误")
	}
}

func TestSmallGameAdapterDisabled(t *testing.T) {
	adapter := NewSmallGameAdapter()
	adapter.SetEnabled(false)

	user := userdata.New()
	user.ID = 12345

	_, _, _, err := adapter.ProcessSmallGameRequest(
		user, &pb.SmallGameBtaReq{GameID: 1001, Choice: "askDouble"},
		"dealer", 100.0, map[string]any{},
	)

	if err == nil {
		t.Error("禁用状态下应该返回错误")
	}

	if !strings.Contains(err.Error(), "未启用") {
		t.Errorf("错误信息应该包含'未启用'，实际错误: %v", err)
	}
}

func BenchmarkSmallGameAdapter(b *testing.B) {
	adapter := NewSmallGameAdapter()
	user := userdata.New()
	user.ID = 12345

	// 初始化用户数据
	if user.SmallGame.SpinData == nil {
		user.SmallGame.SpinData = make(map[int32]string)
	}
	if user.Spin.LastWin == nil {
		user.Spin.LastWin = make(map[int32]float64)
	}

	gameID := int32(1001)
	user.Spin.LastWin[gameID] = 100.0

	spinData := map[string]any{
		"gs": map[string]any{
			"phaseCur":    "basedeal",
			"phaseNext":   "toPaid",
			"subGameInfo": []any{},
		},
	}

	body := &pb.SmallGameBtaReq{
		GameID: gameID,
		Choice: "askDouble",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		adapter.ProcessSmallGameRequest(user, body, "dealer", 100.0, spinData)
	}
}
