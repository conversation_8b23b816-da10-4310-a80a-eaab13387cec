package spin

import (
	"encoding/json"
	"fmt"
)

// SmallGameState 小游戏状态枚举
type SmallGameState int

const (
	StateInit SmallGameState = iota
	StateDoubleEntry
	StateDoubleDealer
	StateDoubleRedBlack
	StateDoubleWin
	StateDoubleLose
	StateBackToGame
	StateFinished
)

func (s SmallGameState) String() string {
	switch s {
	case StateInit:
		return "Init"
	case StateDoubleEntry:
		return "DoubleEntry"
	case StateDoubleDealer:
		return "Doubledealer"
	case StateDoubleRedBlack:
		return "Doubleredblack"
	case StateDoubleWin:
		return "DoubleWin"
	case StateDoubleLose:
		return "DoubleLose"
	case StateBackToGame:
		return "BackToGame"
	case StateFinished:
		return "Finished"
	default:
		return "Unknown"
	}
}

// GameStateNode 游戏状态节点
type GameStateNode struct {
	State       SmallGameState             // 当前状态
	Data        map[string]any             // 状态数据
	Actions     map[string]*GameStateNode  // 可执行的动作及其目标状态
	OnEnter     func(*GameStateNode) error // 进入状态时的回调
	OnExit      func(*GameStateNode) error // 离开状态时的回调
	OnAction    func(string, *GameStateNode) (*GameStateNode, error) // 执行动作时的回调
	Prev        *GameStateNode             // 前一个状态（用于回退）
	GameType    string                     // 游戏类型
	IsTerminal  bool                       // 是否为终止状态
}

// SmallGameChain 小游戏状态链
type SmallGameChain struct {
	Head        *GameStateNode            // 链表头
	Current     *GameStateNode            // 当前状态节点
	States      map[SmallGameState]*GameStateNode // 状态映射
	GameType    string                    // 游戏类型
	GameID      int32                     // 游戏ID
	UserID      int64                     // 用户ID
	InitialWin  float64                   // 初始赢取金额
}

// NewSmallGameChain 创建新的小游戏状态链
func NewSmallGameChain(gameType string, gameID int32, userID int64, initialWin float64) *SmallGameChain {
	chain := &SmallGameChain{
		States:     make(map[SmallGameState]*GameStateNode),
		GameType:   gameType,
		GameID:     gameID,
		UserID:     userID,
		InitialWin: initialWin,
	}
	
	chain.buildStateChain()
	return chain
}

// buildStateChain 构建状态链
func (c *SmallGameChain) buildStateChain() {
	switch c.GameType {
	case "dealer":
		c.buildDealerGameChain()
	case "redblack":
		c.buildRedBlackGameChain()
	default:
		c.buildDefaultChain()
	}
}

// buildDealerGameChain 构建庄家游戏状态链
func (c *SmallGameChain) buildDealerGameChain() {
	// 初始状态
	initNode := &GameStateNode{
		State:    StateInit,
		Data:     make(map[string]any),
		Actions:  make(map[string]*GameStateNode),
		GameType: c.GameType,
	}
	
	// 双倍游戏入口状态
	doubleEntryNode := &GameStateNode{
		State:    StateDoubleEntry,
		Data:     make(map[string]any),
		Actions:  make(map[string]*GameStateNode),
		GameType: c.GameType,
		OnEnter: func(node *GameStateNode) error {
			// 设置初始数据
			node.Data["curWin"] = c.InitialWin
			node.Data["prevWin"] = c.InitialWin
			node.Data["startWin"] = c.InitialWin
			node.Data["category"] = "Double"
			node.Data["type"] = "dealer"
			return nil
		},
	}
	
	// 庄家牌状态
	dealerNode := &GameStateNode{
		State:    StateDoubleDealer,
		Data:     make(map[string]any),
		Actions:  make(map[string]*GameStateNode),
		GameType: c.GameType,
		OnEnter: func(node *GameStateNode) error {
			// 生成庄家牌
			av := generateDealerCard()
			node.Data["av"] = []int{av, 0, 0, 0, 0}
			node.Data["carddealer"] = av
			node.Data["openDealer"] = 1
			node.Data["rule"] = "color"
			return nil
		},
	}
	
	// 获胜状态
	winNode := &GameStateNode{
		State:      StateDoubleWin,
		Data:       make(map[string]any),
		Actions:    make(map[string]*GameStateNode),
		GameType:   c.GameType,
		IsTerminal: false,
		OnEnter: func(node *GameStateNode) error {
			// 计算获胜金额
			if prevWin, ok := node.Prev.Data["curWin"].(float64); ok {
				node.Data["curWin"] = prevWin * 2
				node.Data["paidWin"] = prevWin * 2
			}
			return nil
		},
	}
	
	// 失败状态
	loseNode := &GameStateNode{
		State:      StateDoubleLose,
		Data:       make(map[string]any),
		Actions:    make(map[string]*GameStateNode),
		GameType:   c.GameType,
		IsTerminal: true,
		OnEnter: func(node *GameStateNode) error {
			node.Data["curWin"] = 0
			node.Data["paidWin"] = 0
			return nil
		},
	}
	
	// 返回主游戏状态
	backNode := &GameStateNode{
		State:      StateBackToGame,
		Data:       make(map[string]any),
		Actions:    make(map[string]*GameStateNode),
		GameType:   c.GameType,
		IsTerminal: true,
	}
	
	// 建立状态转换关系
	initNode.Actions["askDouble"] = doubleEntryNode
	doubleEntryNode.Actions["continue"] = dealerNode
	doubleEntryNode.Actions["askDoubleBackToGame"] = backNode
	
	dealerNode.Actions["win"] = winNode
	dealerNode.Actions["lose"] = loseNode
	dealerNode.Actions["back"] = backNode
	
	winNode.Actions["continue"] = dealerNode
	winNode.Actions["collect"] = backNode
	
	// 设置前驱关系
	doubleEntryNode.Prev = initNode
	dealerNode.Prev = doubleEntryNode
	winNode.Prev = dealerNode
	loseNode.Prev = dealerNode
	backNode.Prev = dealerNode
	
	// 存储状态节点
	c.States[StateInit] = initNode
	c.States[StateDoubleEntry] = doubleEntryNode
	c.States[StateDoubleDealer] = dealerNode
	c.States[StateDoubleWin] = winNode
	c.States[StateDoubleLose] = loseNode
	c.States[StateBackToGame] = backNode
	
	// 设置初始状态
	c.Head = initNode
	c.Current = initNode
}

// buildRedBlackGameChain 构建红黑游戏状态链
func (c *SmallGameChain) buildRedBlackGameChain() {
	// 类似的实现，但针对红黑游戏
	// 这里简化实现
	initNode := &GameStateNode{
		State:    StateInit,
		Data:     make(map[string]any),
		Actions:  make(map[string]*GameStateNode),
		GameType: c.GameType,
	}
	
	c.States[StateInit] = initNode
	c.Head = initNode
	c.Current = initNode
}

// buildDefaultChain 构建默认状态链
func (c *SmallGameChain) buildDefaultChain() {
	initNode := &GameStateNode{
		State:    StateInit,
		Data:     make(map[string]any),
		Actions:  make(map[string]*GameStateNode),
		GameType: c.GameType,
	}
	
	c.States[StateInit] = initNode
	c.Head = initNode
	c.Current = initNode
}

// ExecuteAction 执行动作
func (c *SmallGameChain) ExecuteAction(action string) (*GameStateNode, error) {
	if c.Current == nil {
		return nil, fmt.Errorf("当前状态为空")
	}
	
	// 检查动作是否有效
	nextNode, exists := c.Current.Actions[action]
	if !exists {
		return nil, fmt.Errorf("无效的动作: %s，当前状态: %s", action, c.Current.State.String())
	}
	
	// 执行当前状态的退出回调
	if c.Current.OnExit != nil {
		if err := c.Current.OnExit(c.Current); err != nil {
			return nil, fmt.Errorf("退出状态失败: %w", err)
		}
	}
	
	// 执行动作回调
	if c.Current.OnAction != nil {
		resultNode, err := c.Current.OnAction(action, nextNode)
		if err != nil {
			return nil, fmt.Errorf("执行动作失败: %w", err)
		}
		if resultNode != nil {
			nextNode = resultNode
		}
	}
	
	// 切换到下一个状态
	c.Current = nextNode
	
	// 执行新状态的进入回调
	if c.Current.OnEnter != nil {
		if err := c.Current.OnEnter(c.Current); err != nil {
			return nil, fmt.Errorf("进入状态失败: %w", err)
		}
	}
	
	return c.Current, nil
}

// GetCurrentState 获取当前状态
func (c *SmallGameChain) GetCurrentState() *GameStateNode {
	return c.Current
}

// CanExecuteAction 检查是否可以执行指定动作
func (c *SmallGameChain) CanExecuteAction(action string) bool {
	if c.Current == nil {
		return false
	}
	_, exists := c.Current.Actions[action]
	return exists
}

// GetAvailableActions 获取当前状态可执行的动作
func (c *SmallGameChain) GetAvailableActions() []string {
	if c.Current == nil {
		return nil
	}
	
	actions := make([]string, 0, len(c.Current.Actions))
	for action := range c.Current.Actions {
		actions = append(actions, action)
	}
	return actions
}

// IsTerminal 检查是否为终止状态
func (c *SmallGameChain) IsTerminal() bool {
	return c.Current != nil && c.Current.IsTerminal
}

// ToJSON 将当前状态转换为JSON
func (c *SmallGameChain) ToJSON() ([]byte, error) {
	if c.Current == nil {
		return nil, fmt.Errorf("当前状态为空")
	}
	
	result := map[string]any{
		"state":           c.Current.State.String(),
		"data":            c.Current.Data,
		"availableActions": c.GetAvailableActions(),
		"isTerminal":      c.IsTerminal(),
		"gameType":        c.GameType,
	}
	
	return json.Marshal(result)
}

// generateDealerCard 生成庄家牌（临时实现）
func generateDealerCard() int {
	// 这里应该使用之前实现的庄家牌管理系统
	return 25 // 临时返回固定值
}
