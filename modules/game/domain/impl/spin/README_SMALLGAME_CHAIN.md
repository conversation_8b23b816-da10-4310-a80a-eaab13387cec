# 小游戏链表状态管理系统

## 概述

本系统使用链表数据结构实现了小游戏的状态管理，替换了原有的复杂条件判断逻辑。系统具有以下特点：

- **状态机模式**：使用链表节点表示游戏状态，通过状态转换实现游戏流程
- **庄家牌集成**：与 userdata 模块的庄家牌管理系统完全集成
- **向后兼容**：保持与现有 `pb.SmallGameBtaReq` 和 `pb.SmallGameBtaResp` 协议的兼容性
- **错误处理**：提供完善的错误处理和回退机制

## 核心组件

### 1. SmallGameChain (smallgame_chain.go)
链表状态管理的核心实现：

```go
type SmallGameChain struct {
    Head        *GameStateNode            // 链表头
    Current     *GameStateNode            // 当前状态节点
    States      map[SmallGameState]*GameStateNode // 状态映射
    GameType    string                    // 游戏类型
    GameID      int32                     // 游戏ID
    UserID      int64                     // 用户ID
    InitialWin  float64                   // 初始赢取金额
    UserData    *userdata.M               // 用户数据引用
}
```

### 2. SmallGameManager (smallgame_manager.go)
管理多个游戏链的生命周期：

```go
type SmallGameManager struct {
    chains map[string]*SmallGameChain
    mutex  sync.RWMutex
}
```

### 3. SmallGameAdapter (smallgame_adapter.go)
兼容性适配器，将链表实现集成到现有系统：

```go
type SmallGameAdapter struct {
    chainHandler *SmallGameChainHandler
    enabled      bool
}
```

## 状态定义

系统定义了以下游戏状态：

- `StateInit`: 初始状态
- `StateDoubleEntry`: 双倍游戏入口
- `StateDoubleDealer`: 庄家牌状态
- `StateDoubleWin`: 双倍游戏获胜
- `StateDoubleLose`: 双倍游戏失败
- `StateBackToGame`: 返回主游戏

## 集成方式

### 在 onSmallGameBtaReq 函数中的集成

```go
// 尝试使用链表状态管理系统
adapter := NewSmallGameAdapter()
updatedSpinData, chainStatus, chainWin, chainErr := adapter.ProcessSmallGameRequest(
    user, body, smallGameType, lastwin, spinData,
)

if chainErr == nil {
    // 链表处理成功，使用链表结果
    spinData = updatedSpinData
    smallGameStatus = chainStatus
    win = chainWin
} else {
    // 链表处理失败，回退到原始实现
    // ... 原有的 switch 逻辑
}
```

## 庄家牌集成

系统与 userdata 模块的庄家牌管理系统完全集成：

```go
// 在状态节点的 OnEnter 回调中
OnEnter: func(node *GameStateNode) error {
    var av int
    if c.UserData != nil {
        av = generateDealerCardFromUserData(c.UserData, c.GameID)
    } else {
        av = generateDealerCard()
    }
    node.Data["av"] = []int{av, 0, 0, 0, 0}
    node.Data["carddealer"] = av
    node.Data["openDealer"] = 1
    node.Data["rule"] = "color"
    return nil
}
```

## 协议兼容性

### 输入协议 (pb.SmallGameBtaReq)
- `GameID`: 游戏ID
- `Choice`: 用户选择 ("askDouble", "askDoubleBackToGame", "1", "2", "3", "4", "collect", "continue")

### 输出协议 (pb.SmallGameBtaResp)
- `Code`: 响应码
- `Detail`: JSON 格式的详细信息

### spinData 格式兼容性
系统确保生成的 spinData 与现有格式完全兼容：

```json
{
  "gs": {
    "phaseCur": "Doubledealer",
    "phaseNext": "toDouble",
    "curWin": 200.0,
    "subGameInfo": [
      {
        "category": "Double",
        "type": "dealer",
        "curWin": 200.0,
        "av": [25, 0, 0, 0, 0],
        "add": {
          "carddealer": 25,
          "openDealer": 1
        },
        "rule": "color",
        "userChoice": "1"
      }
    ]
  }
}
```

## 错误处理

系统提供多层错误处理：

1. **链表处理失败**：自动回退到原始实现
2. **状态转换错误**：返回适当的错误响应
3. **数据验证错误**：保持现有的验证逻辑

## 性能优化

- **状态缓存**：使用 map 快速查找状态节点
- **并发安全**：使用读写锁保护共享数据
- **内存管理**：及时清理不再使用的游戏链

## 测试

系统包含完整的测试套件：

- **单元测试**：测试各个组件的功能
- **集成测试**：测试与现有系统的集成
- **兼容性测试**：验证协议兼容性
- **性能测试**：基准测试和性能分析

运行测试：
```bash
cd modules/game/domain/impl/spin
go test -v
```

## 配置选项

### 启用/禁用链表实现
```go
adapter := NewSmallGameAdapter()
adapter.SetEnabled(false) // 禁用链表实现，使用原始逻辑
```

### 自定义游戏逻辑
可以通过修改状态节点的回调函数来自定义游戏逻辑：

```go
node.OnEnter = func(node *GameStateNode) error {
    // 自定义进入状态的逻辑
    return nil
}

node.OnExit = func(node *GameStateNode) error {
    // 自定义退出状态的逻辑
    return nil
}
```

## 扩展性

系统设计具有良好的扩展性：

1. **新增状态**：通过添加新的状态枚举和节点
2. **新增游戏类型**：通过扩展状态链构建逻辑
3. **自定义动作**：通过修改动作映射逻辑

## 维护指南

### 添加新状态
1. 在 `SmallGameState` 枚举中添加新状态
2. 在 `buildStateChain` 方法中创建对应节点
3. 设置状态转换逻辑和回调函数

### 修改游戏逻辑
1. 修改对应状态节点的回调函数
2. 更新状态转换条件
3. 确保兼容性测试通过

### 调试技巧
- 使用日志记录状态转换
- 检查 spinData 格式是否正确
- 验证用户数据更新是否正确

## 注意事项

1. **向后兼容性**：任何修改都必须保持与现有协议的兼容性
2. **错误处理**：确保所有错误情况都有适当的处理
3. **性能影响**：监控系统性能，避免引入性能回归
4. **测试覆盖**：新功能必须有相应的测试覆盖

## 联系方式

如有问题或建议，请联系开发团队。
