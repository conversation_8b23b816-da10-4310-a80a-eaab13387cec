package spin

import (
	"encoding/json"
	"testing"
)

func TestSmallGameChainBasic(t *testing.T) {
	// 测试基本的链表创建和状态转换
	chain := NewSmallGameChain("dealer", 1001, 12345, 100.0)

	if chain == nil {
		t.Fatal("链表创建失败")
	}

	if chain.GameType != "dealer" {
		t.<PERSON><PERSON>("游戏类型错误，期望: dealer, 实际: %s", chain.GameType)
	}

	if chain.GameID != 1001 {
		t.<PERSON><PERSON><PERSON>("游戏ID错误，期望: 1001, 实际: %d", chain.GameID)
	}

	if chain.UserID != 12345 {
		t.<PERSON><PERSON><PERSON>("用户ID错误，期望: 12345, 实际: %d", chain.UserID)
	}

	if chain.InitialWin != 100.0 {
		t.<PERSON>rrorf("初始赢取错误，期望: 100.0, 实际: %.2f", chain.InitialWin)
	}

	// 检查状态映射是否正确创建
	if len(chain.States) == 0 {
		t.<PERSON>rror("状态映射不应该为空")
	}

	// 检查是否有初始状态
	if chain.Head == nil {
		t.<PERSON>rror("链表头不应该为空")
	}

	if chain.Current == nil {
		t.<PERSON>rror("当前状态不应该为空")
	}
}

func TestSmallGameChainStateTransition(t *testing.T) {
	chain := NewSmallGameChain("dealer", 1001, 12345, 100.0)

	// 测试状态转换
	initialState := chain.Current.State

	// 尝试执行动作
	nextNode, err := chain.ExecuteAction("askDouble")
	if err != nil {
		t.Fatalf("执行动作失败: %v", err)
	}

	if nextNode == nil {
		t.Error("执行结果不应该为空")
	}

	// 验证状态是否改变
	if chain.Current.State == initialState {
		t.Log("状态可能没有改变，这在某些情况下是正常的")
	}

	// 验证节点结构
	if nextNode != nil {
		if nextNode.State == 0 {
			t.Error("状态节点应该有有效的状态")
		}

		if nextNode.Data == nil {
			t.Error("状态节点应该有数据")
		}
	}
}

func TestSmallGameChainSerialization(t *testing.T) {
	chain := NewSmallGameChain("dealer", 1001, 12345, 100.0)

	// 测试基本的JSON序列化
	chainData := map[string]any{
		"gameType":     chain.GameType,
		"gameID":       chain.GameID,
		"userID":       chain.UserID,
		"initialWin":   chain.InitialWin,
		"currentState": chain.Current.State,
	}

	data, err := json.Marshal(chainData)
	if err != nil {
		t.Fatalf("序列化失败: %v", err)
	}

	if len(data) == 0 {
		t.Error("序列化数据不应该为空")
	}

	// 验证是否为有效的JSON
	var result map[string]any
	err = json.Unmarshal(data, &result)
	if err != nil {
		t.Fatalf("序列化结果不是有效的JSON: %v", err)
	}

	// 验证必要字段
	requiredFields := []string{"gameType", "gameID", "userID", "currentState"}
	for _, field := range requiredFields {
		if _, exists := result[field]; !exists {
			t.Errorf("序列化结果缺少必要字段: %s", field)
		}
	}
}

func TestSmallGameManagerBasic(t *testing.T) {
	manager := NewSmallGameManager()

	if manager == nil {
		t.Fatal("管理器创建失败")
	}

	// 测试获取或创建链表
	chain1 := manager.GetOrCreateChain(1001, 12345, "dealer", 100.0)
	if chain1 == nil {
		t.Error("获取链表失败")
	}

	// 再次获取相同的链表，应该返回同一个实例
	chain2 := manager.GetOrCreateChain(1001, 12345, "dealer", 100.0)
	if chain1 != chain2 {
		t.Error("应该返回相同的链表实例")
	}

	// 获取不同的链表
	chain3 := manager.GetOrCreateChain(1002, 12345, "dealer", 100.0)
	if chain1 == chain3 {
		t.Error("不同的游戏应该返回不同的链表实例")
	}
}

func TestSmallGameChainHandlerBasic(t *testing.T) {
	handler := NewSmallGameChainHandler()

	if handler == nil {
		t.Fatal("处理器创建失败")
	}

	// 由于 HandleSmallGameRequest 需要真实的用户数据结构，
	// 这里只测试处理器的创建
	if handler.manager == nil {
		t.Error("处理器的管理器不应该为空")
	}
}

func TestSmallGameAdapterBasic(t *testing.T) {
	adapter := NewSmallGameAdapter()

	if adapter == nil {
		t.Fatal("适配器创建失败")
	}

	// 测试启用/禁用功能
	adapter.SetEnabled(false)
	if adapter.enabled {
		t.Error("适配器应该被禁用")
	}

	adapter.SetEnabled(true)
	if !adapter.enabled {
		t.Error("适配器应该被启用")
	}
}

func TestSmallGameAdapterChoiceMappingUnit(t *testing.T) {
	adapter := NewSmallGameAdapter()

	testCases := []struct {
		choice       string
		currentState string
		description  string
	}{
		{"askDouble", "Init", "请求双倍游戏"},
		{"askDoubleBackToGame", "DoubleEntry", "返回主游戏"},
		{"1", "Doubledealer", "选择1"},
		{"2", "Doubledealer", "选择2"},
		{"collect", "DoubleWin", "收集奖金"},
		{"continue", "DoubleWin", "继续游戏"},
	}

	for _, tc := range testCases {
		result := adapter.MapChoiceToAction(tc.choice, tc.currentState)

		if result == "" {
			t.Errorf("选择 %s 不应该返回空字符串", tc.choice)
		}

		// 对于数字选择，结果应该是 "win" 或 "lose"
		if tc.choice >= "1" && tc.choice <= "4" {
			if result != "win" && result != "lose" {
				t.Errorf("数字选择 %s 应该返回 win 或 lose，实际返回: %s", tc.choice, result)
			}
		}

		t.Logf("选择 %s -> 动作 %s (%s)", tc.choice, result, tc.description)
	}
}

func TestSmallGameAdapterLegacyStatus(t *testing.T) {
	adapter := NewSmallGameAdapter()

	testCases := []struct {
		state    string
		terminal bool
		expected string
	}{
		{"DoubleWin", false, "win,2"},
		{"DoubleLose", false, "lose,0"},
		{"BackToGame", false, "back,0"},
		{"DoubleEntry", false, "pending,0"},
		{"DoubleEntry", true, "back,0"},
		{"Unknown", false, "unknown,0"},
	}

	for _, tc := range testCases {
		chainResponse := map[string]any{
			"state":    tc.state,
			"terminal": tc.terminal,
		}

		result := adapter.GetLegacyGameStatus(chainResponse)

		if result != tc.expected {
			t.Errorf("状态 %s (terminal: %v) 应该返回 %s，实际返回: %s",
				tc.state, tc.terminal, tc.expected, result)
		}
	}
}

func TestDeepCopyMap(t *testing.T) {
	original := map[string]any{
		"string": "value",
		"number": 42,
		"nested": map[string]any{
			"inner": "value",
		},
		"array": []any{1, 2, 3},
	}

	copied := deepCopyMap(original)

	// 验证是不同的实例
	if &original == &copied {
		t.Error("拷贝应该是不同的实例")
	}

	// 验证内容相同
	if copied["string"] != "value" {
		t.Error("字符串值应该相同")
	}

	if copied["number"] != 42 {
		t.Error("数字值应该相同")
	}

	// 验证嵌套映射是深拷贝
	originalNested := original["nested"].(map[string]any)
	copiedNested := copied["nested"].(map[string]any)

	if &originalNested == &copiedNested {
		t.Error("嵌套映射应该是深拷贝")
	}

	if copiedNested["inner"] != "value" {
		t.Error("嵌套值应该相同")
	}

	// 修改原始数据，验证拷贝不受影响
	originalNested["inner"] = "modified"
	if copiedNested["inner"] == "modified" {
		t.Error("修改原始数据不应该影响拷贝")
	}
}

func BenchmarkSmallGameChainCreation(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		chain := NewSmallGameChain("dealer", int32(1000+i), int64(10000+i), 100.0)
		_ = chain
	}
}

func BenchmarkSmallGameChainExecution(b *testing.B) {
	chain := NewSmallGameChain("dealer", 1001, 12345, 100.0)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// 重置到初始状态
		chain.Current = chain.Head

		// 执行动作
		_, err := chain.ExecuteAction("askDouble")
		if err != nil {
			b.Fatalf("执行动作失败: %v", err)
		}
	}
}

func BenchmarkDeepCopyMap(b *testing.B) {
	testMap := map[string]any{
		"gs": map[string]any{
			"phaseCur":  "basedeal",
			"phaseNext": "toPaid",
			"curWin":    100.0,
			"subGameInfo": []any{
				map[string]any{
					"category": "Double",
					"type":     "dealer",
					"curWin":   100.0,
				},
			},
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = deepCopyMap(testMap)
	}
}
