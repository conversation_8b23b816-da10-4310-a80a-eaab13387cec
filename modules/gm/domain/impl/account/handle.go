package account

import (
	"crypto/md5"
	"encoding/base64"
	"s2/common"
	"s2/common/cache"
	"s2/define"
	"s2/gsconf"
	"s2/pb"
	"slices"
	"sort"
	"strconv"
	"strings"
	"time"

	"s2/mbi"

	"github.com/gin-gonic/gin"
	"github.com/jfcwrlight/core/conf/table"
	"github.com/jfcwrlight/core/infra/chdb"
	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
	"github.com/jfcwrlight/core/utils/hs"
)

func (uc *useCase) onAdminLoginReq(ctx *gin.Context, body *pb.AdminLoginReq) {
	var data []*admin
	err := mdb.Default().Table(TableName).Find(&data, "username = ?", body.Username).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	if len(data) == 0 {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "username not found"})
		return
	}
	sum := md5.Sum([]byte(body.Password))
	hash := base64.StdEncoding.EncodeToString(sum[:])
	if hash != data[0].PwdDigest {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "password error"})
		return
	}
	token := common.GenToken()
	uc.tokenCache.Store(token, body.Username)
	uc.tokenAppIdCache.Store(token, data[0].AppID)
	ctx.JSON(200, pb.AdminLoginResp{
		Code:       pb.SUCCESS,
		Token:      token,
		Username:   body.Username,
		AppID:      data[0].AppID,
		Permission: strings.Split(data[0].Permission, ","),
	})
}

func (uc *useCase) onAdminAccountListReq(ctx *gin.Context, _ *pb.AdminAccountListReq) {
	var data []*admin
	err := mdb.Default().Table(TableName).Find(&data).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	if len(data) == 0 {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "username not found"})
		return
	}
	list := make([]*pb.AdminAccount, 0, len(data))
	for _, item := range data {
		list = append(list, item.ToPB())
	}
	ctx.JSON(200, pb.AdminAccountListResp{
		Code: pb.SUCCESS,
		List: list,
	})
}

func (uc *useCase) onAdminAddAccountReq(ctx *gin.Context, body *pb.AdminAddAccountReq) {
	var data []*admin
	err := mdb.Default().Table(TableName).Find(&data, "username = ?", body.Username).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	if len(data) != 0 {
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR, Msg: "username exist"})
		return
	}
	if len(body.Password) == 0 {
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR, Msg: "password empty"})
		return
	}
	sum := md5.Sum([]byte(body.Password))
	hash := base64.StdEncoding.EncodeToString(sum[:])

	appID := body.AppID
	if appID != "" {
		resp, err := message.RequestAny[pb.GetThirdAppToThirdResp](define.ModuleName.ThirdApp, &pb.GetThirdAppToThirdReq{})
		if err != nil {
			log.Error(err)
			hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
			return
		}
		// 最简洁的存在性检查
		if !slices.ContainsFunc(resp.AppList, func(app *pb.ThirdApp) bool {
			return app.AppID == appID
		}) {
			hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "Third app not found"})
			return
		}
	}

	mdb.Default().Table(TableName).Create(admin{Username: body.Username, PwdDigest: hash, Permission: strings.Join(body.Permission, ","), AppID: body.AppID})
	ctx.JSON(200, &pb.AdminAddAccountResp{
		Code: pb.SUCCESS,
	})
}

func (uc *useCase) onAdminUpdateAccountReq(ctx *gin.Context, body *pb.AdminUpdateAccountReq) {
	var data []*admin
	err := mdb.Default().Table(TableName).Find(&data, "username = ?", body.Username).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	if len(data) == 0 {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "username not found"})
		return
	}
	var hash string
	if len(body.Password) != 0 {
		sum := md5.Sum([]byte(body.Password))
		hash = base64.StdEncoding.EncodeToString(sum[:])
	}
	appID := body.AppID
	if appID != "" {
		resp, err := message.RequestAny[pb.GetThirdAppToThirdResp](define.ModuleName.ThirdApp, &pb.GetThirdAppToThirdReq{})
		if err != nil {
			log.Error(err)
			hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
			return
		}
		// 最简洁的存在性检查
		if !slices.ContainsFunc(resp.AppList, func(app *pb.ThirdApp) bool {
			return app.AppID == appID
		}) {
			hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "Third app not found"})
			return
		}
	}
	mdb.Default().Table(TableName).Updates(admin{Username: body.Username, PwdDigest: hash, Permission: strings.Join(body.Permission, ","), AppID: appID})
	hs.OK(ctx, pb.AdminUpdateAccountResp{
		Code: pb.SUCCESS,
	})
}

func (uc *useCase) onAdminUpdatePwdReq(ctx *gin.Context, body *pb.AdminUpdatePwdReq) {
	token := ctx.Request.Header.Get("Token")
	value, ok := uc.tokenCache.Load(token)
	if !ok {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "token not found"})
		return
	}
	username := value.(string)
	if username != body.Username {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: ""})
		return
	}
	if len(body.Password) == 0 {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "username password empty"})
		return
	}
	sum := md5.Sum([]byte(body.Password))
	hash := base64.StdEncoding.EncodeToString(sum[:])
	err := mdb.Default().Table(TableName).Where("username = ?", username).Update("pwd_digest", hash).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.AdminUpdateAccountResp{
		Code: pb.SUCCESS,
	})
}

func (uc *useCase) onAdminDeleteAccountReq(ctx *gin.Context, body *pb.AdminDeleteAccountReq) {
	err := mdb.Default().Table(TableName).Delete(&admin{Username: body.Username}).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, &pb.AdminUpdateAccountResp{
		Code: pb.SUCCESS,
	})
}

// onGMUserInputHistoryReq handles querying user spin history for admin
func (uc *useCase) onGMUserInputHistoryReq(ctx *gin.Context, body *pb.GMUserInputHistoryReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	if channel != "" {
		body.Channel = channel
	}
	// build dynamic filters and query
	baseCount := chdb.Default().Table(mbi.TableSpinResult)
	baseQuery := chdb.Default().Table(mbi.TableSpinResult).
		Select("private", "user_id", "block_hash", "asset_id", "input", "output", "sn", "create_at", "balance", "mode", "platfrom", "channel", "seat_id", "parent_id", "game_id")
	if body.StartTime == 0 || body.EndTime == 0 {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "start time or end time is empty"})
		return
	}
	if body.StartTime != 0 {
		baseCount = baseCount.Where("create_at >= ?", body.StartTime)
		baseQuery = baseQuery.Where("create_at >= ?", body.StartTime)
	}
	if body.EndTime != 0 {
		baseCount = baseCount.Where("create_at <= ?", body.EndTime)
		baseQuery = baseQuery.Where("create_at <= ?", body.EndTime)
	}
	if body.UserID != 0 {
		baseCount = baseCount.Where("user_id = ?", body.UserID)
		baseQuery = baseQuery.Where("user_id = ?", body.UserID)
	}
	if body.GameID != 0 {
		gID := strconv.Itoa(int(body.GameID))
		baseCount = baseCount.Where("game_id = ?", gID)
		baseQuery = baseQuery.Where("game_id = ?", gID)
	}
	if body.AssetID != 0 {
		assetID := int64(body.AssetID)
		baseCount = baseCount.Where("asset_id = ?", assetID)
		baseQuery = baseQuery.Where("asset_id = ?", assetID)
	}
	if body.ParentID != 0 {
		baseCount = baseCount.Where("parent_id = ?", body.ParentID)
		baseQuery = baseQuery.Where("parent_id = ?", body.ParentID)
	}
	if body.Channel != "" {
		baseCount = baseCount.Where("channel = ?", body.Channel)
		baseQuery = baseQuery.Where("channel = ?", body.Channel)
	}
	// count total records
	var total int64
	err = baseCount.Count(&total).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	// query paginated records
	query := baseQuery.
		Order("create_at DESC").
		Offset(int((body.PageNumber - 1) * body.PageSize)).
		Limit(int(body.PageSize))
	// execute
	var dbRecs []struct {
		Private   string
		UserID    string       `gorm:"user_id"`
		BlockHash string       `gorm:"block_hash"`
		AssetID   pb.EnumAsset `gorm:"asset_id"`
		SN        int32        `gorm:"sn"`
		Input     float64      `gorm:"input"`
		Output    float64      `gorm:"output"`
		Balance   float64      `gorm:"balance"`
		Mode      int32        `gorm:"mode"`
		Platform  string       `gorm:"Platfrom"`
		Channel   string       `gorm:"channel"`
		SeatID    int64        `gorm:"seat_id"`
		ParentID  int64        `gorm:"parent_id"`
		GameID    string       `gorm:"game_id"`
		CreateAt  int64        `gorm:"create_at"`
	}
	err = query.Find(&dbRecs).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	// prepare response records
	respRecs := make([]*pb.GMGameSpinRecord, len(dbRecs))
	for i, r := range dbRecs {
		gameName := r.GameID
		gameID, err := strconv.Atoi(r.GameID)
		if err == nil {
			game := table.Get[gsconf.GameInfoConf](int32(gameID))
			if game != nil {
				gameName = game.Name
			}
		}
		respRecs[i] = &pb.GMGameSpinRecord{
			Private:   r.Private,
			BlockHash: r.BlockHash,
			AssetID:   r.AssetID,
			GameSN:    r.SN,
			Input:     r.Input,
			Output:    r.Output,
			Balance:   r.Balance,
			Mode:      r.Mode,
			Time:      r.CreateAt,
			Platform:  r.Platform,
			Channel:   r.Channel,
			SeatID:    r.SeatID,
			ParentID:  r.ParentID,
			GameID: func() int32 {
				gameid, err := strconv.Atoi(r.GameID)
				if err != nil {
					return 0
				}
				return int32(gameid)
			}(),
			UserId:    r.UserID,
			AssetName: pb.EnumAsset_name[int32(r.AssetID)],
			GameName:  gameName,
		}
	}
	// return
	hs.OK(ctx, pb.GMUserInputHistoryResp{
		Code:    pb.SUCCESS,
		Records: respRecs,
		Total:   total,
	})
}

// onGMAssetHistoryReq handles querying user asset change history for admin
func (uc *useCase) onGMAssetHistoryReq(ctx *gin.Context, body *pb.GMAssetHistoryReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	if channel != "" {
		body.Channel = channel
	}
	// build dynamic filters and query
	baseCount := chdb.Default().Table(mbi.TableUserAssetChange)
	baseQuery := chdb.Default().Table(mbi.TableUserAssetChange).
		Select("event_id", "user_id", "change", "balance", "cause", "channel", "parent_id", "asset_id", "create_at")
	if body.StartTime == 0 {
		body.StartTime = uint64(time.Now().Unix() - 3600*24*30)
	}
	if body.EndTime == 0 {
		body.EndTime = uint64(time.Now().Unix())
	}
	if body.StartTime != 0 {
		baseCount = baseCount.Where("create_at >= ?", body.StartTime)
		baseQuery = baseQuery.Where("create_at >= ?", body.StartTime)
	}
	if body.EndTime != 0 {
		baseCount = baseCount.Where("create_at <= ?", body.EndTime)
		baseQuery = baseQuery.Where("create_at <= ?", body.EndTime)
	}
	if body.UserID != 0 {
		baseCount = baseCount.Where("user_id = ?", body.UserID)
		baseQuery = baseQuery.Where("user_id = ?", body.UserID)
	}
	if body.AssetID != 0 {
		assetID := int64(body.AssetID)
		baseCount = baseCount.Where("asset_id = ?", assetID)
		baseQuery = baseQuery.Where("asset_id = ?", assetID)
	}
	if body.ParentID != 0 {
		baseCount = baseCount.Where("parent_id = ?", body.ParentID)
		baseQuery = baseQuery.Where("parent_id = ?", body.ParentID)
	}
	if body.Channel != "" {
		baseCount = baseCount.Where("channel = ?", body.Channel)
		baseQuery = baseQuery.Where("channel = ?", body.Channel)
	}
	// count total records
	var total int64
	err = baseCount.Count(&total).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	// query paginated records
	query := baseQuery.
		Order("create_at DESC").
		Offset(int((body.PageNumber - 1) * body.PageSize)).
		Limit(int(body.PageSize))
	var dbRecs []struct {
		EventID   uint64
		UserID    int64
		Change    float64
		Balance   float64
		Cause     string
		Channel   string
		ParentID  int64
		AssetID   pb.EnumAsset
		CreatedAt int64 `gorm:"column:create_at"`
	}
	err = query.Find(&dbRecs).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	// prepare response records
	respRecs := make([]*pb.GMAssetHistoryRecord, len(dbRecs))
	for i, r := range dbRecs {
		respRecs[i] = &pb.GMAssetHistoryRecord{
			EventID:  r.EventID,
			UserID:   r.UserID,
			Change:   r.Change,
			Balance:  r.Balance,
			Cause:    r.Cause,
			Channel:  r.Channel,
			ParentID: r.ParentID,
			AssetID:  r.AssetID,
			Time:     r.CreatedAt,
		}
	}
	// return
	hs.OK(ctx, pb.GMAssetHistoryResp{
		Code:    pb.SUCCESS,
		Records: respRecs,
		Total:   total,
	})
}

func (uc *useCase) onGMHistoryWithdrawOrderReq(ctx *gin.Context, body *pb.GMHistoryWithdrawOrderReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	if channel != "" {
		body.Channel = channel
	}
	resp, err := message.RequestAny[pb.HistoryWithdrawOrderResp](define.ModuleName.Order, &pb.HistoryWithdrawOrderReq{
		PageSize:   int32(body.PageSize),
		PageNumber: int32(body.PageNumber),
		StartTime:  body.StartTime,
		EndTime:    body.EndTime,
		Type:       body.Type,
		Channel:    body.Channel,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.GMHistoryWithdrawOrderResp{
		Code:   pb.SUCCESS,
		Orders: resp.Orders,
		Total:  resp.Total,
	})
}

func (uc *useCase) onGMHistoryRechargeOrderReq(ctx *gin.Context, body *pb.GMHistoryRechargeOrderReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	if channel != "" {
		body.Channel = channel
	}
	resp, err := message.RequestAny[pb.HistoryRechargeOrderResp](define.ModuleName.Order, &pb.HistoryRechargeOrderReq{
		PageSize:   int32(body.PageSize),
		PageNumber: int32(body.PageNumber),
		StartTime:  body.StartTime,
		EndTime:    body.EndTime,
		Type:       body.Type,
		Channel:    body.Channel,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.GMHistoryRechargeOrderResp{
		Code:   pb.SUCCESS,
		Orders: resp.Orders,
		Total:  resp.Total,
	})
}

func (uc *useCase) onGMWithdrawOrderOperationReq(ctx *gin.Context, body *pb.GMWithdrawOrderOperationReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	_, err = message.RequestAny[pb.WithdrawOrderOperationResp](define.ModuleName.Order, &pb.WithdrawOrderOperationReq{
		ID:        body.OrderID,
		Operation: body.Operation,
		Channel:   channel,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.GMWithdrawOrderOperationResp{
		Code: pb.SUCCESS,
	})
}

func (uc *useCase) onGMChangeAssetReq(ctx *gin.Context, body *pb.GMChangeAssetReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	if channel != "" {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "channel not allowed"})
		return
	}
	lobbyinfo := table.Get[gsconf.LobbyInfoConf](1)
	assetID := -1
	for _, item := range lobbyinfo.CurrencyList {
		if item == int32(body.Data.ID) {
			assetID = int(item)
		}
	}
	if assetID == -1 {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "asset id not found"})
		return
	}
	bc, err := cache.QueryUserBasicInfo(body.UserID)
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	resp, err := message.Request[pb.AssetBalanceResp](bc.ServerID, &pb.AssetBalanceReq{
		UserID: body.UserID,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	balance := resp.Balance[int32(body.Data.ID)]
	var update float64
	if body.Op == 1 {
		update = body.Data.Value - balance
	} else {
		update = body.Data.Value
	}
	changeAssets := []*pb.IDValFloat{
		&pb.IDValFloat{ID: int64(body.Data.ID), Value: update, LeftInput: 0},
	}
	_, err = message.Request[pb.ChangeAssetResp](bc.ServerID, &pb.ChangeAssetReq{
		UserID: body.UserID,
		Cause:  "GM_ChangeAsset",
		Assets: changeAssets,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.GMChangeAssetResp{
		Code: pb.SUCCESS,
	})
}

func (uc *useCase) onGMChangeLeftInputReq(ctx *gin.Context, body *pb.GMChangeLeftInputReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	if channel != "" {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "channel not allowed"})
		return
	}
	lobbyinfo := table.Get[gsconf.LobbyInfoConf](1)
	assetID := -1
	for _, item := range lobbyinfo.CurrencyList {
		if item == int32(body.Data.ID) {
			assetID = int(item)
		}
	}
	if assetID == -1 {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "asset id not found"})
		return
	}
	bc, err := cache.QueryUserBasicInfo(body.UserID)
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	resp, err := message.Request[pb.AssetResp](bc.ServerID, &pb.AssetReq{
		UserID: body.UserID,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	asset := resp.Assets[int32(body.Data.ID)]
	var update float64
	if body.Op == 1 {
		update = body.Data.LeftInput - asset.LeftInput
		if body.Data.LeftInput < 0 {
			hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "left input < 0"})
			return
		}
	} else {
		update = body.Data.LeftInput
		if body.Data.LeftInput+asset.LeftInput < 0 {
			body.Data.LeftInput = asset.LeftInput
		}
	}
	changeAssets := []*pb.IDValFloat{
		&pb.IDValFloat{ID: int64(body.Data.ID), Value: 0, LeftInput: update},
	}
	_, err = message.Request[pb.ChangeAssetResp](bc.ServerID, &pb.ChangeAssetReq{
		UserID: body.UserID,
		Cause:  "GM_ChangeLeftInput",
		Assets: changeAssets,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.GMChangeLeftInputResp{
		Code: pb.SUCCESS,
	})
}

func (uc *useCase) onGMPlatConfogReq(ctx *gin.Context, body *pb.GMPlatConfogReq) {
	gameList := []*pb.GameConfig{}
	l := table.GetALL[gsconf.GameInfoConf]()
	sort.Slice(l, func(i, j int) bool {
		return l[i].Sort < l[j].Sort
	})
	gameList = append(gameList, &pb.GameConfig{
		GameID:   0,
		GameName: "All",
		Platform: "",
	})
	for _, item := range l {
		gameList = append(gameList, &pb.GameConfig{
			GameID:   item.ID,
			GameName: item.Name,
			Platform: item.Platform,
		})
	}
	lobbyinfo := table.Get[gsconf.LobbyInfoConf](1)
	assetList := []*pb.AssetConfig{}
	for _, item := range lobbyinfo.CurrencyList {
		assetList = append(assetList, &pb.AssetConfig{
			AssetID:   pb.EnumAsset(item),
			AssetName: pb.EnumAsset_name[int32(item)],
		})
	}
	hs.OK(ctx, pb.GMPlatConfogResp{
		Code:      pb.SUCCESS,
		GameList:  gameList,
		AssetList: assetList,
	})
}

func (uc *useCase) onGMSetupGameOddsReq(ctx *gin.Context, body *pb.GMSetupGameOddsReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	if channel != "" {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "channel not allowed"})
		return
	}
	resp, err := message.RequestAny[pb.GMSetupOddsToGameResp](define.ModuleName.Game, &pb.GMSetupOddsToGameReq{
		Data: body.Data,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	gameList := []*pb.GameConfig{}
	l := table.GetALL[gsconf.GameInfoConf]()
	sort.Slice(l, func(i, j int) bool {
		return l[i].Sort < l[j].Sort
	})
	gameList = append(gameList, &pb.GameConfig{
		GameID:   0,
		GameName: "All",
		Platform: "",
	})
	for _, item := range l {
		gameList = append(gameList, &pb.GameConfig{
			GameID:   item.ID,
			GameName: item.Name,
			Platform: item.Platform,
		})
	}
	list := []*pb.GMGameOdds{}
	for _, game := range gameList {
		item := &pb.GMGameOdds{
			GameID:   game.GameID,
			GameName: game.GameName,
			Value:    0,
		}
		for _, item2 := range resp.List {
			if item2.GameID == game.GameID {
				item.Value = item2.Value
				break
			}
		}
		list = append(list, item)
	}
	hs.OK(ctx, pb.GMSetupGameOddsResp{
		Code: resp.Code,
		List: list,
	})
}

func (uc *useCase) onGMUserBaseInfoReq(ctx *gin.Context, body *pb.GMUserBaseInfoReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	bc, err := cache.QueryUserBasicInfo(body.UserID)
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	respAccount, err := message.RequestAny[pb.GMUserBaseInfoToAccountResp](define.ModuleName.Account, &pb.GMUserBaseInfoToAccountReq{
		UserID: body.UserID,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	if channel != "" {
		if respAccount.Data.Channel != channel {
			hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "channel not allowed"})
			return
		}
	}
	respLobby, err := message.Request[pb.GMUserBaseInfoToLobbyResp](bc.ServerID, &pb.GMUserBaseInfoToLobbyReq{
		UserID: body.UserID,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.GMUserBaseInfoResp{
		Code: pb.SUCCESS,
		Data: &pb.GMUserBaseInfo{
			UserID:           respAccount.Data.UserID,
			RegisterPlatform: respAccount.Data.RegisterPlatform,
			Address:          respAccount.Data.Address,
			ParentUid:        respAccount.Data.ParentUid,
			Channel:          respAccount.Data.Channel,
			CreateTime:       respAccount.Data.CreateTime,
			RegisterIP:       respAccount.Data.RegisterIP,
			RegisterDevice:   respAccount.Data.RegisterDevice,

			Name:          respLobby.Data.Name,
			LoginTime:     respLobby.Data.LoginTime,
			LoginIP:       respLobby.Data.LoginIP,
			LoginDevice:   respLobby.Data.LoginDevice,
			Assets:        respLobby.Data.Assets,
			TotalInput:    respLobby.Data.TotalInput,
			TotalOutput:   respLobby.Data.TotalOutput,
			TotalRecharge: respLobby.Data.TotalRecharge,
			TotalWithdraw: respLobby.Data.TotalWithdraw,
			RechargeCount: respLobby.Data.RechargeCount,
			WithdrawCount: respLobby.Data.WithdrawCount,
		}})
}

func (uc *useCase) onGMAddThirdAppToThirdReq(ctx *gin.Context, body *pb.GMAddThirdAppToThirdReq) {
	resp, err := message.RequestAny[pb.AddThirdAppToThirdResp](define.ModuleName.ThirdApp, &pb.AddThirdAppToThirdReq{})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.GMAddThirdAppToThirdResp{
		Code: resp.Code,
	})
}

func (uc *useCase) onGMUpdateThirdAppToThirdReq(ctx *gin.Context, body *pb.GMUpdateThirdAppToThirdReq) {
	resp, err := message.RequestAny[pb.UpdateThirdAppToThirdResp](define.ModuleName.ThirdApp, &pb.UpdateThirdAppToThirdReq{})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.GMUpdateThirdAppToThirdResp{
		Code: resp.Code,
	})
}

func (uc *useCase) onGMGetThirdAppToThirdReq(ctx *gin.Context, body *pb.GMGetThirdAppToThirdReq) {
	resp, err := message.RequestAny[pb.GetThirdAppToThirdResp](define.ModuleName.ThirdApp, &pb.GetThirdAppToThirdReq{})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.GMGetThirdAppToThirdResp{
		Code:    resp.Code,
		AppList: resp.AppList,
	})
}
